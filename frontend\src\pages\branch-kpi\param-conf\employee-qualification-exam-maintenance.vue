<template>
  <div class="report-title">
    <h2>员工资格考试维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        style="width: 100%"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="emp_no" label="员工编号" />
        <el-table-column prop="emp_name" label="员工姓名" />
        <el-table-column prop="fin_anl_exam" label="特许金融分析师考试(CFA)">
          <template #default="scope">
            <el-tag :type="scope.row.fin_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fin_risk_mang_exam" label="金融风险管理师考试(FRM)" >
          <template #default="scope">
            <el-tag :type="scope.row.fin_risk_mang_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_risk_mang_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_advr_exam" label="证券投资顾问考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_advr_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_advr_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_anl_exam" label="证券分析师考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_iq_exam" label="证券从业资格考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fnd_iq_exam" label="基金从业资格考试" >
          <template #default="scope">
            <el-tag :type="scope.row.fnd_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fnd_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_anl_exam" label="证券投资分析考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_exam" label="期货投资分析考试" >
          <template #default="scope">
            <el-tag :type="scope.row.futr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.futr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_pass_mon" label="期货投资分析通过时间" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="info" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="180px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                :disabled="!isEdit"
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分支机构代码" prop="brh_cd">
              <el-select
                v-model="formData.brh_cd"
                filterable
                remote
                reserve-keyword
                placeholder="请选择分支机构"
              >
                <el-option
                  v-for="item in formBranchOptions"
                  :key="item.value"
                  :label="`${item.value} - ${item.label}`"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工编号" prop="emp_no">
              <el-input
                v-model="formData.emp_no"
                placeholder="请输入员工编号"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="特许金融分析师考试(CFA)" prop="fin_anl_exam">
              <el-radio-group v-model="formData.fin_anl_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金融风险管理师考试(FRM)" prop="fin_risk_mang_exam">
              <el-radio-group v-model="formData.fin_risk_mang_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证券投资顾问考试" prop="scr_ivsm_advr_exam">
              <el-radio-group v-model="formData.scr_ivsm_advr_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证券分析师考试" prop="scr_anl_exam">
              <el-radio-group v-model="formData.scr_anl_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证券从业资格考试" prop="scr_iq_exam">
              <el-radio-group v-model="formData.scr_iq_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基金从业资格考试" prop="fnd_iq_exam">
              <el-radio-group v-model="formData.fnd_iq_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证券投资分析考试" prop="scr_ivsm_anl_exam">
              <el-radio-group v-model="formData.scr_ivsm_anl_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期货投资分析考试" prop="futr_ivsm_anl_exam">
              <el-radio-group v-model="formData.futr_ivsm_anl_exam">
                <el-radio value="通过">通过</el-radio>
                <el-radio value="未通过">未通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="期货投资分析通过时间" prop="futr_ivsm_anl_pass_mon">
              <el-date-picker
                v-model="formData.futr_ivsm_anl_pass_mon"
                type="month"
                placeholder="选择通过月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      title="查看历史记录"
      v-model="historyDialogVisible"
      width="1000px"
    >
      <el-table
        :data="historyData"
        border
        stripe
        table-layout="auto"
        style="width: 100%"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="emp_no" label="员工编号" />
        <el-table-column prop="emp_name" label="员工姓名" />
        <el-table-column prop="fin_anl_exam" label="特许金融分析师考试(CFA)">
          <template #default="scope">
            <el-tag :type="scope.row.fin_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fin_risk_mang_exam" label="金融风险管理师考试(FRM)">
          <template #default="scope">
            <el-tag :type="scope.row.fin_risk_mang_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_risk_mang_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_advr_exam" label="证券投资顾问考试">
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_advr_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_advr_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_anl_exam" label="证券分析师考试">
          <template #default="scope">
            <el-tag :type="scope.row.scr_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_iq_exam" label="证券从业资格考试">
          <template #default="scope">
            <el-tag :type="scope.row.scr_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fnd_iq_exam" label="基金从业资格考试">
          <template #default="scope">
            <el-tag :type="scope.row.fnd_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fnd_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_anl_exam" label="证券投资分析考试">
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_exam" label="期货投资分析考试">
          <template #default="scope">
            <el-tag :type="scope.row.futr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.futr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_pass_mon" label="期货投资分析通过时间" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

// 查询表单
const queryForm = reactive({
  branchCode: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 表单专用的分支机构选项
const formBranchOptions = ref([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 表单
const formRef = ref()
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  brh_cd: '',
  brh_nam: '',
  emp_no: '',
  fin_anl_exam: '',
  fin_risk_mang_exam: '',
  scr_ivsm_advr_exam: '',
  scr_anl_exam: '',
  scr_iq_exam: '',
  fnd_iq_exam: '',
  scr_ivsm_anl_exam: '',
  futr_ivsm_anl_exam: '',
  futr_ivsm_anl_pass_mon: '',
  uuid: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  brh_cd: [
    { required: true, message: '请选择分支机构代码', trigger: 'change' }
  ],
  emp_no: [
    { required: true, message: '请输入员工编号', trigger: 'blur' }
  ],
  fin_anl_exam: [
    { required: true, message: '请选择特许金融分析师考试结果', trigger: 'change' }
  ],
  fin_risk_mang_exam: [
    { required: true, message: '请选择金融风险管理师考试结果', trigger: 'change' }
  ],
  scr_ivsm_advr_exam: [
    { required: true, message: '请选择证券投资顾问考试结果', trigger: 'change' }
  ],
  scr_anl_exam: [
    { required: true, message: '请选择证券分析师考试结果', trigger: 'change' }
  ],
  scr_iq_exam: [
    { required: true, message: '请选择证券从业资格考试结果', trigger: 'change' }
  ],
  fnd_iq_exam: [
    { required: true, message: '请选择基金从业资格考试结果', trigger: 'change' }
  ],
  scr_ivsm_anl_exam: [
    { required: true, message: '请选择证券投资分析考试结果', trigger: 'change' }
  ],
  futr_ivsm_anl_exam: [
    { required: true, message: '请选择期货投资分析考试结果', trigger: 'change' }
  ]
}

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}





// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }
    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.branchCode) {
      // 支持多个分支机构代码（逗号分隔）
      if (queryForm.branchCode.includes(',')) {
        const branchCodesStr = queryForm.branchCode.split(',').map(code => `"${code.trim()}"`).join(',')
        filters.brh_cd = `in.(${branchCodesStr})`
      } else {
        filters.brh_cd = `eq.${queryForm.branchCode}`
      }
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_emp_qlfy_exam_mtc', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增员工资格考试维护'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改员工资格考试维护'
  isEdit.value = true
  resetForm()

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该员工的所有历史记录
    const config = {
      params: {
        emp_no: `eq.${row.emp_no}`,
        order: 'crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get('/v_emp_qlfy_exam_mtc', {}, config)
    const data = response.data || []

    historyData.value = data
    historyDialogVisible.value = true

  } catch (error) {
    console.error('获取历史记录时发生错误:', error)
    ElMessage.error('获取历史记录失败，请检查网络连接')
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.branchCode) {
      // 支持多个分支机构代码（逗号分隔）
      if (queryForm.branchCode.includes(',')) {
        const branchCodesStr = queryForm.branchCode.split(',').map(code => `"${code.trim()}"`).join(',')
        filters.brh_cd = `in.(${branchCodesStr})`
      } else {
        filters.brh_cd = `eq.${queryForm.branchCode}`
      }
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_emp_qlfy_exam_mtc', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const excelData = [
      // 表头
      ['生效开始日期', '生效结束日期', '分支机构代码', '分支机构名称', '员工编号', '员工姓名',
       '特许金融分析师考试(CFA)', '金融风险管理师考试(FRM)', '证券投资顾问考试', '证券分析师考试',
       '证券从业资格考试', '基金从业资格考试', '证券投资分析考试', '期货投资分析考试',
       '期货投资分析通过时间', '创建时间', '更新时间'],
      // 数据行
      ...allData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.brh_cd || '',
        item.brh_nam || '',
        item.emp_no || '',
        item.emp_name || '',
        item.fin_anl_exam || '',
        item.fin_risk_mang_exam || '',
        item.scr_ivsm_advr_exam || '',
        item.scr_anl_exam || '',
        item.scr_iq_exam || '',
        item.fnd_iq_exam || '',
        item.scr_ivsm_anl_exam || '',
        item.futr_ivsm_anl_exam || '',
        item.futr_ivsm_anl_pass_mon || '',
        formatDateTime(item.crt_time),
        formatDateTime(item.upd_time)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 15 }, // 分支机构代码
      { wch: 20 }, // 分支机构名称
      { wch: 15 }, // 员工编号
      { wch: 12 }, // 员工姓名
      { wch: 20 }, // 特许金融分析师考试(CFA)
      { wch: 20 }, // 金融风险管理师考试(FRM)
      { wch: 15 }, // 证券投资顾问考试
      { wch: 15 }, // 证券分析师考试
      { wch: 15 }, // 证券从业资格考试
      { wch: 15 }, // 基金从业资格考试
      { wch: 15 }, // 证券投资分析考试
      { wch: 15 }, // 期货投资分析考试
      { wch: 18 }, // 期货投资分析通过时间
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '员工资格考试维护')

    // 生成文件名
    const now = new Date()
    const fileName = `员工资格考试维护_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacde,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            brh_cd: formData.brh_cd,
            emp_no: formData.emp_no,
            fin_anl_exam: formData.fin_anl_exam,
            fin_risk_mang_exam: formData.fin_risk_mang_exam,
            scr_ivsm_advr_exam: formData.scr_ivsm_advr_exam,
            scr_anl_exam: formData.scr_anl_exam,
            scr_iq_exam: formData.scr_iq_exam,
            fnd_iq_exam: formData.fnd_iq_exam,
            scr_ivsm_anl_exam: formData.scr_ivsm_anl_exam,
            futr_ivsm_anl_exam: formData.futr_ivsm_anl_exam,
            futr_ivsm_anl_pass_mon: formData.futr_ivsm_anl_pass_mon
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await http.post('/rpc/p_emp_qlfy_exam_mtc_e', requestData, {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        })

        if (response.data && response.data.o_status === 0) {
          const result = response.data

          if (result.o_status === 0) {
            ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
            dialogVisible.value = false
            handleQuery() // 重新查询数据
          } else {
            ElMessage.error(result.o_msg || '操作失败')
          }
        } else {
          ElMessage.error(response.data?.o_msg || '操作失败')
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacde,
        uuid: row.uuid
      }
    }

    const response = await http.post('/rpc/p_emp_qlfy_exam_mtc_e', requestData, {
      headers: {
        'Content-Profile': 'mkt_base',
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.o_status === 0) {
      const result = response.data

      if (result.o_status === 0) {
        ElMessage.success('删除成功')
        handleQuery() // 重新查询数据
      } else {
        ElMessage.error(result.o_msg || '删除失败')
      }
    } else {
      ElMessage.error('删除失败，请检查网络连接')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brh_cd: '',
    brh_nam: '',
    emp_no: '',
    fin_anl_exam: '',
    fin_risk_mang_exam: '',
    scr_ivsm_advr_exam: '',
    scr_anl_exam: '',
    scr_iq_exam: '',
    fnd_iq_exam: '',
    scr_ivsm_anl_exam: '',
    futr_ivsm_anl_exam: '',
    futr_ivsm_anl_pass_mon: '',
    uuid: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 原始的开始日期（编辑时保存）
const originalStartDate = ref(""); // 例如 '2024-03'

// 远程加载或初始化时设置原始值
function openEditDialog(row) {
  dialogTitle.value = "修改";
  isEdit.value = true;

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date; // 如 '2024-03'

  formData.value = { ...row };
  dialogVisible.value = true;
}

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value || !isEdit.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};
</script>

<style lang="scss" scoped>
/* empty */
</style>
