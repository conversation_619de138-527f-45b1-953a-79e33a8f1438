<template>
  <div class="report-title">
    <h2>营销人员必选指标目标下限维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="岗位职级:">
              <el-select
                v-model="queryForm.postLevel"
                placeholder="请选择岗位职级"
                multiple
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in postLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="net_add_equi_lowl" label="净增日均权益月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.net_add_equi_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="net_incm_lowl" label="净收入月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.net_incm_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="naad_effh_lowl" label="新增有效户月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.naad_effh_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <el-button
              type="success"
              size="small"
              @click="handleHistory(scope.row)"
              >历史记录</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="180px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                :disabled="!isEdit"
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="岗位职级" prop="post_lvl">
              <el-select
                v-model="formData.post_lvl"
                placeholder="请选择岗位职级"
                :disabled="isEdit"
                style="width: 100%"
              >
                <el-option
                  v-for="item in postLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="净增日均权益月目标下限" prop="net_add_equi_lowl">
              <el-input-number
                v-model="formData.net_add_equi_lowl"
                :min="0"
                :step="0.01"
                :precision="2"
                placeholder="请输入净增日均权益月目标下限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净收入月目标下限" prop="net_incm_lowl">
              <el-input-number
                v-model="formData.net_incm_lowl"
                :min="0"
                :step="0.01"
                :precision="2"
                placeholder="请输入净收入月目标下限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="新增有效户月目标下限" prop="naad_effh_lowl">
              <el-input-number
                v-model="formData.naad_effh_lowl"
                :min="0"
                :step="0.01"
                :precision="2"
                placeholder="请输入新增有效户月目标下限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="历史记录" width="1000px">
      <el-table :data="historyData" border stripe table-layout="auto">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="net_add_equi_lowl" label="净增日均权益月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.net_add_equi_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="net_incm_lowl" label="净收入月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.net_incm_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="naad_effh_lowl" label="新增有效户月目标下限" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.naad_effh_lowl) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as XLSX from "xlsx";
import http from "~/http/http.js";

// 数字格式化函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') return ''
  return Number(value).toFixed(2)
}

// 查询表单
const queryForm = reactive({
  postLevel: [],
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 岗位职级选项
const postLevelOptions = ref([]);

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacode: params.get("oacode") || "xurr1",
    roleid: params.get("roleid") || "001",
  };
};

const urlParams = getUrlParams();

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const isEdit = ref(false);

// 历史记录对话框
const historyDialogVisible = ref(false);
const historyData = ref([]);

// 表单数据
const formData = reactive({
  tect_strt_date: "",
  tect_end_date: "",
  post_lvl: "",
  net_add_equi_lowl: null,
  net_incm_lowl: null,
  naad_effh_lowl: null,
  uuid: "",
});

// 原始的开始日期（编辑时保存）
const originalStartDate = ref("");

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: "请选择生效开始日期", trigger: "change" },
  ],
  tect_end_date: [
    { required: true, message: "请选择生效结束日期", trigger: "change" },
  ],
  post_lvl: [
    { required: true, message: "请输入岗位职级", trigger: "blur" },
  ],
  net_add_equi_lowl: [
    { required: true, message: "请输入净增日均权益月目标下限", trigger: "blur" },
    { type: "number", min: 0, message: "净增日均权益月目标下限必须大于等于0", trigger: "blur" },
  ],
  net_incm_lowl: [
    { required: true, message: "请输入净收入月目标下限", trigger: "blur" },
    { type: "number", min: 0, message: "净收入月目标下限必须大于等于0", trigger: "blur" },
  ],
  naad_effh_lowl: [
    { required: true, message: "请输入新增有效户月目标下限", trigger: "blur" },
    { type: "number", min: 0, message: "新增有效户月目标下限必须大于等于0", trigger: "blur" },
  ],
};

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return (
    formData.tect_strt_date &&
    formData.tect_end_date &&
    formData.post_lvl &&
    formData.net_add_equi_lowl !== null &&
    formData.net_add_equi_lowl !== undefined &&
    formData.net_incm_lowl !== null &&
    formData.net_incm_lowl !== undefined &&
    formData.naad_effh_lowl !== null &&
    formData.naad_effh_lowl !== undefined &&
    formData.net_add_equi_lowl >= 0 &&
    formData.net_incm_lowl >= 0 &&
    formData.naad_effh_lowl >= 0
  );
});

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value || !isEdit.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};

// 加载岗位职级字典数据
const loadPostLevelOptions = async () => {
  try {
    const filters = {
      dict_code: "eq.rykh0004",
    };

    const config = {
      params: {
        ...filters,
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    const response = await http.get("/v_dictionary_cfg", {}, config);
    const data = response.data || [];
    postLevelOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  } catch (error) {
    console.error("加载岗位职级字典数据失败:", error);
    ElMessage.error("加载岗位职级字典数据失败");
  }
};

// 初始化数据
onMounted(async () => {
  await loadPostLevelOptions();
  handleQuery();
});

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {};

  // 添加查询条件 - 需要根据选择的item_code查询对应的item_value
  if (queryForm.postLevel && queryForm.postLevel.length > 0) {
    // 将选择的item_code转换为对应的item_value进行查询
    const selectedLabels = queryForm.postLevel.map(code => {
      const option = postLevelOptions.value.find(opt => opt.value === code);
      return option ? option.label : code;
    });
    if (selectedLabels.length === 1) {
      filters.post_lvl = `eq.${selectedLabels[0]}`;
    } else {
      filters.post_lvl = `in.(${selectedLabels.join(',')})`;
    }
  }

  // 添加last_flag=1条件
  filters.last_flag = "eq.1";

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value;
  const limit = pageSize.value;

  const config = {
    params: {
      ...filters,
      order: "tect_strt_date.desc,crt_time.desc",
    },
    headers: {
      Accept: "application/json",
      Range: `${offset}-${offset + limit - 1}`,
      "Accept-Profile": "mkt_base",
    },
  };

  http
    .get("/v_mngr_fix_indx_lowl_mtc", {}, config)
    .then((response) => {
      tableData.value = response.data || [];
      totalCount.value = response.total || 0;
    })
    .catch((error) => {
      console.error("API请求失败:", error);
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false;
    });
};

// 新增
const handleAdd = () => {
  dialogTitle.value = "新增营销人员必选指标目标下限维护";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 修改
const handleEdit = (row) => {
  dialogTitle.value = "修改营销人员必选指标目标下限维护";
  isEdit.value = true;
  resetForm();

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(formData, row);

  // 将显示的岗位职级名称转换回代码值用于表单编辑
  const postLevelOption = postLevelOptions.value.find(opt => opt.label === row.post_lvl);
  if (postLevelOption) {
    formData.post_lvl = postLevelOption.value;
  }

  dialogVisible.value = true;
};

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该岗位职级的所有历史记录
    const filters = {
      post_lvl: `eq.${row.post_lvl}`,
    };

    const config = {
      params: {
        ...filters,
        order: "tect_strt_date.desc,crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    const response = await http.get("/v_mngr_fix_indx_lowl_mtc", {}, config);
    historyData.value = response.data || [];
    historyDialogVisible.value = true;
  } catch (error) {
    console.error("获取历史记录失败:", error);
    ElMessage.error("获取历史记录失败");
  }
};

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      "此操作将永久删除该记录, 是否继续?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacode,
        uuid: row.uuid,
      },
    };

    const response = await http.post("/rpc/p_mngr_fix_indx_lowl_mtc_e", requestData, {
      headers: {
        "Content-Type": "application/json",
        "Content-Profile": "mkt_base",
      },
    });

    if (response.data && response.data.o_status === 0) {
      ElMessage.success("删除成功");
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(`删除失败: ${response.data?.o_msg || "未知错误"}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    const requestData = {
      i_request: {
        optionflg: isEdit.value ? "2" : "1",
        oacode: urlParams.oacode,
        tect_strt_date: formData.tect_strt_date,
        tect_end_date: formData.tect_end_date,
        post_lvl: formData.post_lvl,
        net_add_equi_lowl: formData.net_add_equi_lowl,
        net_incm_lowl: formData.net_incm_lowl,
        naad_effh_lowl: formData.naad_effh_lowl,
      },
    };

    // 如果是修改，添加uuid
    if (isEdit.value) {
      requestData.i_request.uuid = formData.uuid;
    }

    const response = await http.post("/rpc/p_mngr_fix_indx_lowl_mtc_e", requestData, {
      headers: {
        "Content-Type": "application/json",
        "Content-Profile": "mkt_base",
      },
    });

    if (response.data && response.data.o_status === 0) {
      ElMessage.success(isEdit.value ? "修改成功" : "新增成功");
      dialogVisible.value = false;
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(
        `${isEdit.value ? "修改" : "新增"}失败: ${
          response.data?.o_msg || "未知错误"
        }`
      );
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    tect_strt_date: "",
    tect_end_date: "",
    post_lvl: "",
    net_add_equi_lowl: null,
    net_incm_lowl: null,
    naad_effh_lowl: null,
    uuid: "",
  });
  originalStartDate.value = "";
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 对话框关闭
const handleDialogClose = () => {
  resetForm();
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    // 构建查询参数，获取所有数据（不分页）
    const filters = {};

    // 添加查询条件 - 需要根据选择的item_code查询对应的item_value
    if (queryForm.postLevel && queryForm.postLevel.length > 0) {
      // 将选择的item_code转换为对应的item_value进行查询
      const selectedLabels = queryForm.postLevel.map(code => {
        const option = postLevelOptions.value.find(opt => opt.value === code);
        return option ? option.label : code;
      });
      if (selectedLabels.length === 1) {
        filters.post_lvl = `eq.${selectedLabels[0]}`;
      } else {
        filters.post_lvl = `in.(${selectedLabels.join(',')})`;
      }
    }

    // 添加last_flag=1条件
    filters.last_flag = "eq.1";

    const config = {
      params: {
        ...filters,
        order: "tect_strt_date.desc,crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    // 获取所有数据
    const response = await http.get("/v_mngr_fix_indx_lowl_mtc", {}, config);
    const allData = response.data || [];

    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "生效开始日期",
        "生效结束日期",
        "岗位职级",
        "净增日均权益月目标下限",
        "净收入月目标下限",
        "新增有效户月目标下限",
        "创建时间",
        "更新时间",
      ],
      // 数据行
      ...allData.map((item) => [
        item.tect_strt_date || "",
        item.tect_end_date || "",
        item.post_lvl || "",
        formatNumber(item.net_add_equi_lowl),
        formatNumber(item.net_incm_lowl),
        formatNumber(item.naad_effh_lowl),
        item.crt_time || "",
        item.upd_time || "",
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 岗位职级
      { wch: 25 }, // 净增日均权益月目标下限
      { wch: 20 }, // 净收入月目标下限
      { wch: 25 }, // 新增有效户月目标下限
      { wch: 20 }, // 创建时间
      { wch: 20 }, // 更新时间
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "营销人员必选指标目标下限维护");

    // 生成文件名
    const now = new Date();
    const fileName = `营销人员必选指标目标下限维护${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};
</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .query-card {
    margin-bottom: 20px;

    .query-form {
      .button-group {
        text-align: right;
        margin-top: 10px;
      }
    }
  }

  .table-card {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
