<template>
  <el-config-provider namespace="ep">
    <!-- 独立页面：不显示导航栏和侧边栏 -->
    <div v-if="isStandalonePage" class="standalone-page">
      <BaseHeader />
      <RouterView />
    </div>
    <!-- 普通页面：显示完整布局 -->
    <div v-else>
      <BaseHeader />
      <div class="main-container flex">
        <BaseSide />
        <div w="full" py="4">
          <RouterView />
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

// 定义需要独立显示的页面路径
const standalonePages = [
  "/branch-kpi/dashboard",
  // "/branch-kpi/branch-kpi-completion-status",
  // "/branch-kpi/param-conf/param-weight",
  // "/branch-kpi/param-conf/branch-rating-setting",
  // "/branch-kpi/param-conf/business-equity-conversion-setting",
  // "/branch-kpi/param-conf/ib-workload-weight-setting",
  // "/branch-kpi/param-conf/bonus-provision-ratio",
  // "/branch-kpi/param-conf/ib-serv-prsn-mtc",
  // "/branch-kpi/param-conf/branch-ib-integration-relation-maintenance",
  // "/branch-kpi/param-conf/employee-qualification-exam-maintenance",
  // "/branch-kpi/param-conf/assessment-related-regulation-maintenance",
  // "/branch-kpi/kpi-items/assessment-related-regulation-query",
  // "/branch-kpi/kpi-items/exam-financial-report",
  // "/branch-kpi/kpi-items/exam-financial-report-branch",
  // "/branch-kpi/kpi-items/employee-qualification-exam-statistics",
  // "/branch-kpi/kpi-items/other-add-subtract-points",
  // "/branch-kpi/kpi-items/customer-data-statistics",
  // "/branch-kpi/kpi-items/customer-data-statistics-test",
  // "/branch-kpi/kpi-items/new-valid-customer-statistics",
  // "/branch-kpi/kpi-items/ib-workload-score",
  // "/branch-kpi/kpi-items/ib-business-achievement-statistics",
  // "/branch-kpi/kpi-items/ib-workload-statistics",
  // "/branch-kpi/kpi-items/wealth-management-statistics",
  // "/branch-kpi/kpi-items/risk-management-statistics",
  // "/branch-kpi/system-data-audits/ib-business-income-report",
  // "/branch-kpi/system-data-audits/employee-info-query",
  // "/branch-kpi/system-data-audits/capital-statement-daily",
  // "/branch-kpi/system-data-audits/business-person-relation-query",
  // "/branch-kpi/system-data-audits/retired-employee-info-query",
  // "/branch-kpi/system-data-audits/simple-employee-info-query",
  // "/branch-kpi/system-data-audits/employee-info-all-query",
  // "/branch-kpi/assessment-scoring-accounting/branch-assessment-score",
  // "/branch-kpi/assessment-scoring-accounting/branch-assessment-score-detail",
  // "/branch-kpi/kpi-items/employee_info_statistics",
  // "/branch-kpi/kpi-items/team_construction_score",
  // "/branch-kpi/kpi-items/market_share_stats",
  // "/branch-kpi/manual-entry/branch-exam-target-entry",
  // "/branch-kpi/manual-entry/financial-data-entry",
  // "/branch-kpi/manual-entry/financial-exam-entry",
  // "/branch-kpi/manual-entry/financial-manage-entry",
  // "/branch-kpi/manual-entry/risk-manage-entry",
  // "/branch-kpi/manual-entry/exchange-proj-entry",
  // "/branch-kpi/manual-entry/ib-word-situ",
  // "/branch-kpi/manual-entry/ib-busi-aet",
  // "/branch-kpi/manual-entry/mkt-prsn-ior",
  // "/branch-kpi/manual-entry/prsn-vol-scor",
  // "/branch-kpi/manual-entry/mkt-mtch",
  // "/branch-kpi/manual-entry/oth-aasp",
  // "/branch-kpi/data-check-cal/basic-data-preparation",
  // "/branch-kpi/data-check-cal/etl-task",
  // "/branch-kpi/data-check-cal/etl-task-his",
  // "/branch-kpi/kpi-items/initial-financial-report",
  // "/branch-kpi/system-data-audit",
  // "/branch-kpi/import-audt",
  // "/branch-performance/quarter-performance",
  // "/branch-performance/quarter-performance-center",
  // "/branch-performance/ib-quarter-performance",
  // "/branch-performance/ib-quarter-performance-center",
  // "/branch-performance/quarterly-bonus-accounting/branch-monthly-profit-statistics",
  // "/branch-performance/quarterly-bonus-accounting/ib-fusion-performance-bonus",
  // "/branch-performance/quarterly-bonus-accounting/branch-quarterly-bonus-accounting",
  // "/branch-performance/data-check-cal/kpi-etl-task"
];

// 判断当前页面是否为独立页面
const isStandalonePage = computed(() => {
  return standalonePages.includes(route.path);
});
</script>

<style>
#app {
  text-align: center;
  color: var(--ep-text-color-primary);
}

.main-container {
  height: calc(100vh - var(--ep-menu-item-height) - 4px);
}

.standalone-page {
  width: 100%;
  height: 100vh;
  overflow: auto;
}
</style>