{"version": 3, "sources": ["../../@unhead/shared/dist/index.mjs", "../../@unhead/dom/dist/index.mjs", "../../hookable/dist/index.mjs", "../../unhead/dist/index.mjs", "../../@unhead/vue/dist/shared/vue.ziyDaVMR.mjs", "../../@unhead/vue/dist/index.mjs", "../../vite-ssg/dist/shared/vite-ssg.ETIvV-80.mjs", "../../vite-ssg/dist/shared/vite-ssg.C6pK7rvr.mjs", "../../vite-ssg/dist/index.mjs"], "sourcesContent": ["import { unpackToString, unpackToArray, packArray } from 'packrup';\n\nfunction asArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nconst SelfClosingTags = /* @__PURE__ */ new Set([\"meta\", \"link\", \"base\"]);\nconst TagsWithInnerContent = /* @__PURE__ */ new Set([\"title\", \"titleTemplate\", \"script\", \"style\", \"noscript\"]);\nconst HasElementTags = /* @__PURE__ */ new Set([\n  \"base\",\n  \"meta\",\n  \"link\",\n  \"style\",\n  \"script\",\n  \"noscript\"\n]);\nconst ValidHeadTags = /* @__PURE__ */ new Set([\n  \"title\",\n  \"titleTemplate\",\n  \"templateParams\",\n  \"base\",\n  \"htmlAttrs\",\n  \"bodyAttrs\",\n  \"meta\",\n  \"link\",\n  \"style\",\n  \"script\",\n  \"noscript\"\n]);\nconst UniqueTags = /* @__PURE__ */ new Set([\"base\", \"title\", \"titleTemplate\", \"bodyAttrs\", \"htmlAttrs\", \"templateParams\"]);\nconst TagConfigKeys = /* @__PURE__ */ new Set([\"tagPosition\", \"tagPriority\", \"tagDuplicateStrategy\", \"children\", \"innerHTML\", \"textContent\", \"processTemplateParams\"]);\nconst IsBrowser = typeof window !== \"undefined\";\nconst composableNames = [\n  \"getActiveHead\",\n  \"useHead\",\n  \"useSeoMeta\",\n  \"useHeadSafe\",\n  \"useServerHead\",\n  \"useServerSeoMeta\",\n  \"useServerHeadSafe\"\n];\n\nfunction defineHeadPlugin(plugin) {\n  return plugin;\n}\n\nfunction hashCode(s) {\n  let h = 9;\n  for (let i = 0; i < s.length; )\n    h = Math.imul(h ^ s.charCodeAt(i++), 9 ** 9);\n  return ((h ^ h >>> 9) + 65536).toString(16).substring(1, 8).toLowerCase();\n}\nfunction hashTag(tag) {\n  if (tag._h) {\n    return tag._h;\n  }\n  if (tag._d) {\n    return hashCode(tag._d);\n  }\n  let content = `${tag.tag}:${tag.textContent || tag.innerHTML || \"\"}:`;\n  for (const key in tag.props) {\n    content += `${key}:${String(tag.props[key])},`;\n  }\n  return hashCode(content);\n}\n\nconst p = (p2) => ({ keyValue: p2, metaKey: \"property\" });\nconst k = (p2) => ({ keyValue: p2 });\nconst MetaPackingSchema = {\n  appleItunesApp: {\n    unpack: {\n      entrySeparator: \", \",\n      resolve({ key, value }) {\n        return `${fixKeyCase(key)}=${value}`;\n      }\n    }\n  },\n  articleExpirationTime: p(\"article:expiration_time\"),\n  articleModifiedTime: p(\"article:modified_time\"),\n  articlePublishedTime: p(\"article:published_time\"),\n  bookReleaseDate: p(\"book:release_date\"),\n  charset: {\n    metaKey: \"charset\"\n  },\n  contentSecurityPolicy: {\n    unpack: {\n      entrySeparator: \"; \",\n      resolve({ key, value }) {\n        return `${fixKeyCase(key)} ${value}`;\n      }\n    },\n    metaKey: \"http-equiv\"\n  },\n  contentType: {\n    metaKey: \"http-equiv\"\n  },\n  defaultStyle: {\n    metaKey: \"http-equiv\"\n  },\n  fbAppId: p(\"fb:app_id\"),\n  msapplicationConfig: k(\"msapplication-Config\"),\n  msapplicationTileColor: k(\"msapplication-TileColor\"),\n  msapplicationTileImage: k(\"msapplication-TileImage\"),\n  ogAudioSecureUrl: p(\"og:audio:secure_url\"),\n  ogAudioUrl: p(\"og:audio\"),\n  ogImageSecureUrl: p(\"og:image:secure_url\"),\n  ogImageUrl: p(\"og:image\"),\n  ogSiteName: p(\"og:site_name\"),\n  ogVideoSecureUrl: p(\"og:video:secure_url\"),\n  ogVideoUrl: p(\"og:video\"),\n  profileFirstName: p(\"profile:first_name\"),\n  profileLastName: p(\"profile:last_name\"),\n  profileUsername: p(\"profile:username\"),\n  refresh: {\n    metaKey: \"http-equiv\",\n    unpack: {\n      entrySeparator: \";\",\n      resolve({ key, value }) {\n        if (key === \"seconds\")\n          return `${value}`;\n      }\n    }\n  },\n  robots: {\n    unpack: {\n      entrySeparator: \", \",\n      resolve({ key, value }) {\n        if (typeof value === \"boolean\")\n          return `${fixKeyCase(key)}`;\n        else\n          return `${fixKeyCase(key)}:${value}`;\n      }\n    }\n  },\n  xUaCompatible: {\n    metaKey: \"http-equiv\"\n  }\n};\nconst openGraphNamespaces = /* @__PURE__ */ new Set([\n  \"og\",\n  \"book\",\n  \"article\",\n  \"profile\"\n]);\nfunction resolveMetaKeyType(key) {\n  const fKey = fixKeyCase(key);\n  const prefixIndex = fKey.indexOf(\":\");\n  if (openGraphNamespaces.has(fKey.substring(0, prefixIndex)))\n    return \"property\";\n  return MetaPackingSchema[key]?.metaKey || \"name\";\n}\nfunction resolveMetaKeyValue(key) {\n  return MetaPackingSchema[key]?.keyValue || fixKeyCase(key);\n}\nfunction fixKeyCase(key) {\n  const updated = key.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n  const prefixIndex = updated.indexOf(\"-\");\n  const fKey = updated.substring(0, prefixIndex);\n  if (fKey === \"twitter\" || openGraphNamespaces.has(fKey))\n    return key.replace(/([A-Z])/g, \":$1\").toLowerCase();\n  return updated;\n}\nfunction changeKeyCasingDeep(input) {\n  if (Array.isArray(input)) {\n    return input.map((entry) => changeKeyCasingDeep(entry));\n  }\n  if (typeof input !== \"object\" || Array.isArray(input))\n    return input;\n  const output = {};\n  for (const key in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, key)) {\n      continue;\n    }\n    output[fixKeyCase(key)] = changeKeyCasingDeep(input[key]);\n  }\n  return output;\n}\nfunction resolvePackedMetaObjectValue(value, key) {\n  const definition = MetaPackingSchema[key];\n  if (key === \"refresh\")\n    return `${value.seconds};url=${value.url}`;\n  return unpackToString(\n    changeKeyCasingDeep(value),\n    {\n      keyValueSeparator: \"=\",\n      entrySeparator: \", \",\n      resolve({ value: value2, key: key2 }) {\n        if (value2 === null)\n          return \"\";\n        if (typeof value2 === \"boolean\")\n          return `${key2}`;\n      },\n      ...definition?.unpack\n    }\n  );\n}\nconst ObjectArrayEntries = /* @__PURE__ */ new Set([\"og:image\", \"og:video\", \"og:audio\", \"twitter:image\"]);\nfunction sanitize(input) {\n  const out = {};\n  for (const k2 in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, k2)) {\n      continue;\n    }\n    const v = input[k2];\n    if (String(v) !== \"false\" && k2)\n      out[k2] = v;\n  }\n  return out;\n}\nfunction handleObjectEntry(key, v) {\n  const value = sanitize(v);\n  const fKey = fixKeyCase(key);\n  const attr = resolveMetaKeyType(fKey);\n  if (ObjectArrayEntries.has(fKey)) {\n    const input = {};\n    for (const k2 in value) {\n      if (!Object.prototype.hasOwnProperty.call(value, k2)) {\n        continue;\n      }\n      input[`${key}${k2 === \"url\" ? \"\" : `${k2[0].toUpperCase()}${k2.slice(1)}`}`] = value[k2];\n    }\n    return unpackMeta(input).sort((a, b) => (a[attr]?.length || 0) - (b[attr]?.length || 0));\n  }\n  return [{ [attr]: fKey, ...value }];\n}\nfunction unpackMeta(input) {\n  const extras = [];\n  const primitives = {};\n  for (const key in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, key)) {\n      continue;\n    }\n    const value = input[key];\n    if (!Array.isArray(value)) {\n      if (typeof value === \"object\" && value) {\n        if (ObjectArrayEntries.has(fixKeyCase(key))) {\n          extras.push(...handleObjectEntry(key, value));\n          continue;\n        }\n        primitives[key] = sanitize(value);\n      } else {\n        primitives[key] = value;\n      }\n      continue;\n    }\n    for (const v of value) {\n      extras.push(...typeof v === \"string\" ? unpackMeta({ [key]: v }) : handleObjectEntry(key, v));\n    }\n  }\n  const meta = unpackToArray(primitives, {\n    key({ key }) {\n      return resolveMetaKeyType(key);\n    },\n    value({ key }) {\n      return key === \"charset\" ? \"charset\" : \"content\";\n    },\n    resolveKeyData({ key }) {\n      return resolveMetaKeyValue(key);\n    },\n    resolveValueData({ value, key }) {\n      if (value === null)\n        return \"_null\";\n      if (typeof value === \"object\")\n        return resolvePackedMetaObjectValue(value, key);\n      return typeof value === \"number\" ? value.toString() : value;\n    }\n  });\n  return [...extras, ...meta].map((m) => {\n    if (m.content === \"_null\")\n      m.content = null;\n    return m;\n  });\n}\nfunction packMeta(inputs) {\n  const mappedPackingSchema = Object.entries(MetaPackingSchema).map(([key, value]) => [key, value.keyValue]);\n  return packArray(inputs, {\n    key: [\"name\", \"property\", \"httpEquiv\", \"http-equiv\", \"charset\"],\n    value: [\"content\", \"charset\"],\n    resolveKey(k2) {\n      let key = mappedPackingSchema.filter((sk) => sk[1] === k2)?.[0]?.[0] || k2;\n      const replacer = (_, letter) => letter?.toUpperCase();\n      key = key.replace(/:([a-z])/g, replacer).replace(/-([a-z])/g, replacer);\n      return key;\n    }\n  });\n}\n\nfunction thenable(val, thenFn) {\n  if (val instanceof Promise) {\n    return val.then(thenFn);\n  }\n  return thenFn(val);\n}\n\nfunction normaliseTag(tagName, input, e, normalizedProps) {\n  const props = normalizedProps || normaliseProps(\n    // explicitly check for an object\n    // @ts-expect-error untyped\n    typeof input === \"object\" && typeof input !== \"function\" && !(input instanceof Promise) ? { ...input } : { [tagName === \"script\" || tagName === \"noscript\" || tagName === \"style\" ? \"innerHTML\" : \"textContent\"]: input },\n    tagName === \"templateParams\" || tagName === \"titleTemplate\"\n  );\n  if (props instanceof Promise) {\n    return props.then((val) => normaliseTag(tagName, input, e, val));\n  }\n  const tag = {\n    tag: tagName,\n    props\n  };\n  for (const k of TagConfigKeys) {\n    const val = tag.props[k] !== void 0 ? tag.props[k] : e[k];\n    if (val !== void 0) {\n      if (!(k === \"innerHTML\" || k === \"textContent\" || k === \"children\") || TagsWithInnerContent.has(tag.tag)) {\n        tag[k === \"children\" ? \"innerHTML\" : k] = val;\n      }\n      delete tag.props[k];\n    }\n  }\n  if (tag.props.body) {\n    tag.tagPosition = \"bodyClose\";\n    delete tag.props.body;\n  }\n  if (tag.tag === \"script\") {\n    if (typeof tag.innerHTML === \"object\") {\n      tag.innerHTML = JSON.stringify(tag.innerHTML);\n      tag.props.type = tag.props.type || \"application/json\";\n    }\n  }\n  return Array.isArray(tag.props.content) ? tag.props.content.map((v) => ({ ...tag, props: { ...tag.props, content: v } })) : tag;\n}\nfunction normaliseStyleClassProps(key, v) {\n  const sep = key === \"class\" ? \" \" : \";\";\n  if (v && typeof v === \"object\" && !Array.isArray(v)) {\n    v = Object.entries(v).filter(([, v2]) => v2).map(([k, v2]) => key === \"style\" ? `${k}:${v2}` : k);\n  }\n  return String(Array.isArray(v) ? v.join(sep) : v)?.split(sep).filter((c) => Boolean(c.trim())).join(sep);\n}\nfunction nestedNormaliseProps(props, virtual, keys, startIndex) {\n  for (let i = startIndex; i < keys.length; i += 1) {\n    const k = keys[i];\n    if (k === \"class\" || k === \"style\") {\n      props[k] = normaliseStyleClassProps(k, props[k]);\n      continue;\n    }\n    if (props[k] instanceof Promise) {\n      return props[k].then((val) => {\n        props[k] = val;\n        return nestedNormaliseProps(props, virtual, keys, i);\n      });\n    }\n    if (!virtual && !TagConfigKeys.has(k)) {\n      const v = String(props[k]);\n      const isDataKey = k.startsWith(\"data-\");\n      if (v === \"true\" || v === \"\") {\n        props[k] = isDataKey ? \"true\" : true;\n      } else if (!props[k]) {\n        if (isDataKey && v === \"false\")\n          props[k] = \"false\";\n        else\n          delete props[k];\n      }\n    }\n  }\n}\nfunction normaliseProps(props, virtual = false) {\n  const resolvedProps = nestedNormaliseProps(props, virtual, Object.keys(props), 0);\n  if (resolvedProps instanceof Promise) {\n    return resolvedProps.then(() => props);\n  }\n  return props;\n}\nconst TagEntityBits = 10;\nfunction nestedNormaliseEntryTags(headTags, tagPromises, startIndex) {\n  for (let i = startIndex; i < tagPromises.length; i += 1) {\n    const tags = tagPromises[i];\n    if (tags instanceof Promise) {\n      return tags.then((val) => {\n        tagPromises[i] = val;\n        return nestedNormaliseEntryTags(headTags, tagPromises, i);\n      });\n    }\n    if (Array.isArray(tags)) {\n      headTags.push(...tags);\n    } else {\n      headTags.push(tags);\n    }\n  }\n}\nfunction normaliseEntryTags(e) {\n  const tagPromises = [];\n  const input = e.resolvedInput;\n  for (const k in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, k)) {\n      continue;\n    }\n    const v = input[k];\n    if (v === void 0 || !ValidHeadTags.has(k)) {\n      continue;\n    }\n    if (Array.isArray(v)) {\n      for (const props of v) {\n        tagPromises.push(normaliseTag(k, props, e));\n      }\n      continue;\n    }\n    tagPromises.push(normaliseTag(k, v, e));\n  }\n  if (tagPromises.length === 0) {\n    return [];\n  }\n  const headTags = [];\n  return thenable(nestedNormaliseEntryTags(headTags, tagPromises, 0), () => headTags.map((t, i) => {\n    t._e = e._i;\n    e.mode && (t._m = e.mode);\n    t._p = (e._i << TagEntityBits) + i;\n    return t;\n  }));\n}\n\nconst WhitelistAttributes = {\n  htmlAttrs: [\"id\", \"class\", \"lang\", \"dir\"],\n  bodyAttrs: [\"id\", \"class\"],\n  meta: [\"id\", \"name\", \"property\", \"charset\", \"content\"],\n  noscript: [\"id\", \"textContent\"],\n  script: [\"id\", \"type\", \"textContent\"],\n  link: [\"id\", \"color\", \"crossorigin\", \"fetchpriority\", \"href\", \"hreflang\", \"imagesrcset\", \"imagesizes\", \"integrity\", \"media\", \"referrerpolicy\", \"rel\", \"sizes\", \"type\"]\n};\nfunction acceptDataAttrs(value) {\n  const filtered = {};\n  Object.keys(value || {}).filter((a) => a.startsWith(\"data-\")).forEach((a) => {\n    filtered[a] = value[a];\n  });\n  return filtered;\n}\nfunction whitelistSafeInput(input) {\n  const filtered = {};\n  Object.keys(input).forEach((key) => {\n    const tagValue = input[key];\n    if (!tagValue)\n      return;\n    switch (key) {\n      // always safe\n      case \"title\":\n      case \"titleTemplate\":\n      case \"templateParams\":\n        filtered[key] = tagValue;\n        break;\n      case \"htmlAttrs\":\n      case \"bodyAttrs\":\n        filtered[key] = acceptDataAttrs(tagValue);\n        WhitelistAttributes[key].forEach((a) => {\n          if (tagValue[a])\n            filtered[key][a] = tagValue[a];\n        });\n        break;\n      // meta is safe, except for http-equiv\n      case \"meta\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const safeMeta = acceptDataAttrs(meta);\n            WhitelistAttributes.meta.forEach((key2) => {\n              if (meta[key2])\n                safeMeta[key2] = meta[key2];\n            });\n            return safeMeta;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n      // link tags we don't allow stylesheets, scripts, preloading, prerendering, prefetching, etc\n      case \"link\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const link = acceptDataAttrs(meta);\n            WhitelistAttributes.link.forEach((key2) => {\n              const val = meta[key2];\n              if (key2 === \"rel\" && (val === \"stylesheet\" || val === \"canonical\" || val === \"modulepreload\" || val === \"prerender\" || val === \"preload\" || val === \"prefetch\"))\n                return;\n              if (key2 === \"href\") {\n                if (val.includes(\"javascript:\") || val.includes(\"data:\"))\n                  return;\n                link[key2] = val;\n              } else if (val) {\n                link[key2] = val;\n              }\n            });\n            return link;\n          }).filter((link) => Object.keys(link).length > 1 && !!link.rel);\n        }\n        break;\n      case \"noscript\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const noscript = acceptDataAttrs(meta);\n            WhitelistAttributes.noscript.forEach((key2) => {\n              if (meta[key2])\n                noscript[key2] = meta[key2];\n            });\n            return noscript;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n      // we only allow JSON in scripts\n      case \"script\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((script) => {\n            const safeScript = acceptDataAttrs(script);\n            WhitelistAttributes.script.forEach((s) => {\n              if (script[s]) {\n                if (s === \"textContent\") {\n                  try {\n                    const jsonVal = typeof script[s] === \"string\" ? JSON.parse(script[s]) : script[s];\n                    safeScript[s] = JSON.stringify(jsonVal, null, 0);\n                  } catch (e) {\n                  }\n                } else {\n                  safeScript[s] = script[s];\n                }\n              }\n            });\n            return safeScript;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n    }\n  });\n  return filtered;\n}\n\nconst NetworkEvents = /* @__PURE__ */ new Set([\"onload\", \"onerror\", \"onabort\", \"onprogress\", \"onloadstart\"]);\nconst ScriptNetworkEvents = /* @__PURE__ */ new Set([\"onload\", \"onerror\"]);\n\nconst TAG_WEIGHTS = {\n  // tags\n  base: -10,\n  title: 10\n};\nconst TAG_ALIASES = {\n  // relative scores to their default values\n  critical: -80,\n  high: -10,\n  low: 20\n};\nfunction tagWeight(tag) {\n  const priority = tag.tagPriority;\n  if (typeof priority === \"number\")\n    return priority;\n  let weight = 100;\n  if (tag.tag === \"meta\") {\n    if (tag.props[\"http-equiv\"] === \"content-security-policy\")\n      weight = -30;\n    else if (tag.props.charset)\n      weight = -20;\n    else if (tag.props.name === \"viewport\")\n      weight = -15;\n  } else if (tag.tag === \"link\" && tag.props.rel === \"preconnect\") {\n    weight = 20;\n  } else if (tag.tag in TAG_WEIGHTS) {\n    weight = TAG_WEIGHTS[tag.tag];\n  }\n  if (priority && priority in TAG_ALIASES) {\n    return weight + TAG_ALIASES[priority];\n  }\n  return weight;\n}\nconst SortModifiers = [{ prefix: \"before:\", offset: -1 }, { prefix: \"after:\", offset: 1 }];\n\nconst allowedMetaProperties = [\"name\", \"property\", \"http-equiv\"];\nfunction tagDedupeKey(tag) {\n  const { props, tag: tagName } = tag;\n  if (UniqueTags.has(tagName))\n    return tagName;\n  if (tagName === \"link\" && props.rel === \"canonical\")\n    return \"canonical\";\n  if (props.charset)\n    return \"charset\";\n  if (props.id) {\n    return `${tagName}:id:${props.id}`;\n  }\n  for (const n of allowedMetaProperties) {\n    if (props[n] !== void 0) {\n      return `${tagName}:${n}:${props[n]}`;\n    }\n  }\n  return false;\n}\n\nconst sepSub = \"%separator\";\nfunction sub(p, token, isJson = false) {\n  let val;\n  if (token === \"s\" || token === \"pageTitle\") {\n    val = p.pageTitle;\n  } else if (token.includes(\".\")) {\n    const dotIndex = token.indexOf(\".\");\n    val = p[token.substring(0, dotIndex)]?.[token.substring(dotIndex + 1)];\n  } else {\n    val = p[token];\n  }\n  if (val !== void 0) {\n    return isJson ? (val || \"\").replace(/\"/g, '\\\\\"') : val || \"\";\n  }\n  return void 0;\n}\nconst sepSubRe = new RegExp(`${sepSub}(?:\\\\s*${sepSub})*`, \"g\");\nfunction processTemplateParams(s, p, sep, isJson = false) {\n  if (typeof s !== \"string\" || !s.includes(\"%\"))\n    return s;\n  let decoded = s;\n  try {\n    decoded = decodeURI(s);\n  } catch {\n  }\n  const tokens = decoded.match(/%\\w+(?:\\.\\w+)?/g);\n  if (!tokens) {\n    return s;\n  }\n  const hasSepSub = s.includes(sepSub);\n  s = s.replace(/%\\w+(?:\\.\\w+)?/g, (token) => {\n    if (token === sepSub || !tokens.includes(token)) {\n      return token;\n    }\n    const re = sub(p, token.slice(1), isJson);\n    return re !== void 0 ? re : token;\n  }).trim();\n  if (hasSepSub) {\n    if (s.endsWith(sepSub))\n      s = s.slice(0, -sepSub.length);\n    if (s.startsWith(sepSub))\n      s = s.slice(sepSub.length);\n    s = s.replace(sepSubRe, sep).trim();\n  }\n  return s;\n}\n\nfunction resolveTitleTemplate(template, title) {\n  if (template == null)\n    return title || null;\n  if (typeof template === \"function\")\n    return template(title);\n  return template;\n}\n\nexport { HasElementTags, IsBrowser, NetworkEvents, ScriptNetworkEvents, SelfClosingTags, SortModifiers, TAG_ALIASES, TAG_WEIGHTS, TagConfigKeys, TagEntityBits, TagsWithInnerContent, UniqueTags, ValidHeadTags, asArray, composableNames, defineHeadPlugin, hashCode, hashTag, normaliseEntryTags, normaliseProps, normaliseStyleClassProps, normaliseTag, packMeta, processTemplateParams, resolveMetaKeyType, resolveMetaKeyValue, resolvePackedMetaObjectValue, resolveTitleTemplate, tagDedupeKey, tagWeight, thenable, unpackMeta, whitelistSafeInput };\n", "import { HasElementTags, hashTag, normaliseProps, tagDedupeKey, defineHeadPlugin } from '@unhead/shared';\n\nasync function renderDOMHead(head, options = {}) {\n  const dom = options.document || head.resolvedOptions.document;\n  if (!dom || !head.dirty)\n    return;\n  const beforeRenderCtx = { shouldRender: true, tags: [] };\n  await head.hooks.callHook(\"dom:beforeRender\", beforeRenderCtx);\n  if (!beforeRenderCtx.shouldRender)\n    return;\n  if (head._domUpdatePromise) {\n    return head._domUpdatePromise;\n  }\n  head._domUpdatePromise = new Promise(async (resolve) => {\n    const tags = (await head.resolveTags()).map((tag) => ({\n      tag,\n      id: HasElementTags.has(tag.tag) ? hashTag(tag) : tag.tag,\n      shouldRender: true\n    }));\n    let state = head._dom;\n    if (!state) {\n      state = {\n        elMap: { htmlAttrs: dom.documentElement, bodyAttrs: dom.body }\n      };\n      const takenDedupeKeys = /* @__PURE__ */ new Set();\n      for (const key of [\"body\", \"head\"]) {\n        const children = dom[key]?.children;\n        for (const c of children) {\n          const tag = c.tagName.toLowerCase();\n          if (!HasElementTags.has(tag)) {\n            continue;\n          }\n          const t = {\n            tag,\n            props: await normaliseProps(\n              c.getAttributeNames().reduce((props, name) => ({ ...props, [name]: c.getAttribute(name) }), {})\n            ),\n            innerHTML: c.innerHTML\n          };\n          const dedupeKey = tagDedupeKey(t);\n          let d = dedupeKey;\n          let i = 1;\n          while (d && takenDedupeKeys.has(d))\n            d = `${dedupeKey}:${i++}`;\n          if (d) {\n            t._d = d;\n            takenDedupeKeys.add(d);\n          }\n          state.elMap[c.getAttribute(\"data-hid\") || hashTag(t)] = c;\n        }\n      }\n    }\n    state.pendingSideEffects = { ...state.sideEffects };\n    state.sideEffects = {};\n    function track(id, scope, fn) {\n      const k = `${id}:${scope}`;\n      state.sideEffects[k] = fn;\n      delete state.pendingSideEffects[k];\n    }\n    function trackCtx({ id, $el, tag }) {\n      const isAttrTag = tag.tag.endsWith(\"Attrs\");\n      state.elMap[id] = $el;\n      if (!isAttrTag) {\n        if (tag.textContent && tag.textContent !== $el.textContent) {\n          $el.textContent = tag.textContent;\n        }\n        if (tag.innerHTML && tag.innerHTML !== $el.innerHTML) {\n          $el.innerHTML = tag.innerHTML;\n        }\n        track(id, \"el\", () => {\n          state.elMap[id]?.remove();\n          delete state.elMap[id];\n        });\n      }\n      if (tag._eventHandlers) {\n        for (const k in tag._eventHandlers) {\n          if (!Object.prototype.hasOwnProperty.call(tag._eventHandlers, k)) {\n            continue;\n          }\n          if ($el.getAttribute(`data-${k}`) !== \"\") {\n            (tag.tag === \"bodyAttrs\" ? dom.defaultView : $el).addEventListener(\n              // onload -> load\n              k.substring(2),\n              tag._eventHandlers[k].bind($el)\n            );\n            $el.setAttribute(`data-${k}`, \"\");\n          }\n        }\n      }\n      for (const k in tag.props) {\n        if (!Object.prototype.hasOwnProperty.call(tag.props, k)) {\n          continue;\n        }\n        const value = tag.props[k];\n        const ck = `attr:${k}`;\n        if (k === \"class\") {\n          if (!value) {\n            continue;\n          }\n          for (const c of value.split(\" \")) {\n            isAttrTag && track(id, `${ck}:${c}`, () => $el.classList.remove(c));\n            !$el.classList.contains(c) && $el.classList.add(c);\n          }\n        } else if (k === \"style\") {\n          if (!value) {\n            continue;\n          }\n          for (const c of value.split(\";\")) {\n            const propIndex = c.indexOf(\":\");\n            const k2 = c.substring(0, propIndex).trim();\n            const v = c.substring(propIndex + 1).trim();\n            track(id, `${ck}:${k2}`, () => {\n              $el.style.removeProperty(k2);\n            });\n            $el.style.setProperty(k2, v);\n          }\n        } else {\n          $el.getAttribute(k) !== value && $el.setAttribute(k, value === true ? \"\" : String(value));\n          isAttrTag && track(id, ck, () => $el.removeAttribute(k));\n        }\n      }\n    }\n    const pending = [];\n    const frag = {\n      bodyClose: void 0,\n      bodyOpen: void 0,\n      head: void 0\n    };\n    for (const ctx of tags) {\n      const { tag, shouldRender, id } = ctx;\n      if (!shouldRender)\n        continue;\n      if (tag.tag === \"title\") {\n        dom.title = tag.textContent;\n        continue;\n      }\n      ctx.$el = ctx.$el || state.elMap[id];\n      if (ctx.$el) {\n        trackCtx(ctx);\n      } else if (HasElementTags.has(tag.tag)) {\n        pending.push(ctx);\n      }\n    }\n    for (const ctx of pending) {\n      const pos = ctx.tag.tagPosition || \"head\";\n      ctx.$el = dom.createElement(ctx.tag.tag);\n      trackCtx(ctx);\n      frag[pos] = frag[pos] || dom.createDocumentFragment();\n      frag[pos].appendChild(ctx.$el);\n    }\n    for (const ctx of tags)\n      await head.hooks.callHook(\"dom:renderTag\", ctx, dom, track);\n    frag.head && dom.head.appendChild(frag.head);\n    frag.bodyOpen && dom.body.insertBefore(frag.bodyOpen, dom.body.firstChild);\n    frag.bodyClose && dom.body.appendChild(frag.bodyClose);\n    for (const k in state.pendingSideEffects) {\n      state.pendingSideEffects[k]();\n    }\n    head._dom = state;\n    await head.hooks.callHook(\"dom:rendered\", { renders: tags });\n    resolve();\n  }).finally(() => {\n    head._domUpdatePromise = void 0;\n    head.dirty = false;\n  });\n  return head._domUpdatePromise;\n}\n\nfunction debouncedRenderDOMHead(head, options = {}) {\n  const fn = options.delayFn || ((fn2) => setTimeout(fn2, 10));\n  return head._domDebouncedUpdatePromise = head._domDebouncedUpdatePromise || new Promise((resolve) => fn(() => {\n    return renderDOMHead(head, options).then(() => {\n      delete head._domDebouncedUpdatePromise;\n      resolve();\n    });\n  }));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction DomPlugin(options) {\n  return defineHeadPlugin((head) => {\n    const initialPayload = head.resolvedOptions.document?.head.querySelector('script[id=\"unhead:payload\"]')?.innerHTML || false;\n    if (initialPayload) {\n      head.push(JSON.parse(initialPayload));\n    }\n    return {\n      mode: \"client\",\n      hooks: {\n        \"entries:updated\": (head2) => {\n          debouncedRenderDOMHead(head2, options);\n        }\n      }\n    };\n  });\n}\n\nexport { DomPlugin, debouncedRenderDOMHead, renderDOMHead };\n", "function flatHooks(configHooks, hooks = {}, parentName) {\n  for (const key in configHooks) {\n    const subHook = configHooks[key];\n    const name = parentName ? `${parentName}:${key}` : key;\n    if (typeof subHook === \"object\" && subHook !== null) {\n      flatHooks(subHook, hooks, name);\n    } else if (typeof subHook === \"function\") {\n      hooks[name] = subHook;\n    }\n  }\n  return hooks;\n}\nfunction mergeHooks(...hooks) {\n  const finalHooks = {};\n  for (const hook of hooks) {\n    const flatenHook = flatHooks(hook);\n    for (const key in flatenHook) {\n      if (finalHooks[key]) {\n        finalHooks[key].push(flatenHook[key]);\n      } else {\n        finalHooks[key] = [flatenHook[key]];\n      }\n    }\n  }\n  for (const key in finalHooks) {\n    if (finalHooks[key].length > 1) {\n      const array = finalHooks[key];\n      finalHooks[key] = (...arguments_) => serial(array, (function_) => function_(...arguments_));\n    } else {\n      finalHooks[key] = finalHooks[key][0];\n    }\n  }\n  return finalHooks;\n}\nfunction serial(tasks, function_) {\n  return tasks.reduce(\n    (promise, task) => promise.then(() => function_(task)),\n    Promise.resolve()\n  );\n}\nconst defaultTask = { run: (function_) => function_() };\nconst _createTask = () => defaultTask;\nconst createTask = typeof console.createTask !== \"undefined\" ? console.createTask : _createTask;\nfunction serialTaskCaller(hooks, args) {\n  const name = args.shift();\n  const task = createTask(name);\n  return hooks.reduce(\n    (promise, hookFunction) => promise.then(() => task.run(() => hookFunction(...args))),\n    Promise.resolve()\n  );\n}\nfunction parallelTaskCaller(hooks, args) {\n  const name = args.shift();\n  const task = createTask(name);\n  return Promise.all(hooks.map((hook) => task.run(() => hook(...args))));\n}\nfunction serialCaller(hooks, arguments_) {\n  return hooks.reduce(\n    (promise, hookFunction) => promise.then(() => hookFunction(...arguments_ || [])),\n    Promise.resolve()\n  );\n}\nfunction parallelCaller(hooks, args) {\n  return Promise.all(hooks.map((hook) => hook(...args || [])));\n}\nfunction callEachWith(callbacks, arg0) {\n  for (const callback of [...callbacks]) {\n    callback(arg0);\n  }\n}\n\nclass Hookable {\n  constructor() {\n    this._hooks = {};\n    this._before = void 0;\n    this._after = void 0;\n    this._deprecatedMessages = void 0;\n    this._deprecatedHooks = {};\n    this.hook = this.hook.bind(this);\n    this.callHook = this.callHook.bind(this);\n    this.callHookWith = this.callHookWith.bind(this);\n  }\n  hook(name, function_, options = {}) {\n    if (!name || typeof function_ !== \"function\") {\n      return () => {\n      };\n    }\n    const originalName = name;\n    let dep;\n    while (this._deprecatedHooks[name]) {\n      dep = this._deprecatedHooks[name];\n      name = dep.to;\n    }\n    if (dep && !options.allowDeprecated) {\n      let message = dep.message;\n      if (!message) {\n        message = `${originalName} hook has been deprecated` + (dep.to ? `, please use ${dep.to}` : \"\");\n      }\n      if (!this._deprecatedMessages) {\n        this._deprecatedMessages = /* @__PURE__ */ new Set();\n      }\n      if (!this._deprecatedMessages.has(message)) {\n        console.warn(message);\n        this._deprecatedMessages.add(message);\n      }\n    }\n    if (!function_.name) {\n      try {\n        Object.defineProperty(function_, \"name\", {\n          get: () => \"_\" + name.replace(/\\W+/g, \"_\") + \"_hook_cb\",\n          configurable: true\n        });\n      } catch {\n      }\n    }\n    this._hooks[name] = this._hooks[name] || [];\n    this._hooks[name].push(function_);\n    return () => {\n      if (function_) {\n        this.removeHook(name, function_);\n        function_ = void 0;\n      }\n    };\n  }\n  hookOnce(name, function_) {\n    let _unreg;\n    let _function = (...arguments_) => {\n      if (typeof _unreg === \"function\") {\n        _unreg();\n      }\n      _unreg = void 0;\n      _function = void 0;\n      return function_(...arguments_);\n    };\n    _unreg = this.hook(name, _function);\n    return _unreg;\n  }\n  removeHook(name, function_) {\n    if (this._hooks[name]) {\n      const index = this._hooks[name].indexOf(function_);\n      if (index !== -1) {\n        this._hooks[name].splice(index, 1);\n      }\n      if (this._hooks[name].length === 0) {\n        delete this._hooks[name];\n      }\n    }\n  }\n  deprecateHook(name, deprecated) {\n    this._deprecatedHooks[name] = typeof deprecated === \"string\" ? { to: deprecated } : deprecated;\n    const _hooks = this._hooks[name] || [];\n    delete this._hooks[name];\n    for (const hook of _hooks) {\n      this.hook(name, hook);\n    }\n  }\n  deprecateHooks(deprecatedHooks) {\n    Object.assign(this._deprecatedHooks, deprecatedHooks);\n    for (const name in deprecatedHooks) {\n      this.deprecateHook(name, deprecatedHooks[name]);\n    }\n  }\n  addHooks(configHooks) {\n    const hooks = flatHooks(configHooks);\n    const removeFns = Object.keys(hooks).map(\n      (key) => this.hook(key, hooks[key])\n    );\n    return () => {\n      for (const unreg of removeFns.splice(0, removeFns.length)) {\n        unreg();\n      }\n    };\n  }\n  removeHooks(configHooks) {\n    const hooks = flatHooks(configHooks);\n    for (const key in hooks) {\n      this.removeHook(key, hooks[key]);\n    }\n  }\n  removeAllHooks() {\n    for (const key in this._hooks) {\n      delete this._hooks[key];\n    }\n  }\n  callHook(name, ...arguments_) {\n    arguments_.unshift(name);\n    return this.callHookWith(serialTaskCaller, name, ...arguments_);\n  }\n  callHookParallel(name, ...arguments_) {\n    arguments_.unshift(name);\n    return this.callHookWith(parallelTaskCaller, name, ...arguments_);\n  }\n  callHookWith(caller, name, ...arguments_) {\n    const event = this._before || this._after ? { name, args: arguments_, context: {} } : void 0;\n    if (this._before) {\n      callEachWith(this._before, event);\n    }\n    const result = caller(\n      name in this._hooks ? [...this._hooks[name]] : [],\n      arguments_\n    );\n    if (result instanceof Promise) {\n      return result.finally(() => {\n        if (this._after && event) {\n          callEachWith(this._after, event);\n        }\n      });\n    }\n    if (this._after && event) {\n      callEachWith(this._after, event);\n    }\n    return result;\n  }\n  beforeEach(function_) {\n    this._before = this._before || [];\n    this._before.push(function_);\n    return () => {\n      if (this._before !== void 0) {\n        const index = this._before.indexOf(function_);\n        if (index !== -1) {\n          this._before.splice(index, 1);\n        }\n      }\n    };\n  }\n  afterEach(function_) {\n    this._after = this._after || [];\n    this._after.push(function_);\n    return () => {\n      if (this._after !== void 0) {\n        const index = this._after.indexOf(function_);\n        if (index !== -1) {\n          this._after.splice(index, 1);\n        }\n      }\n    };\n  }\n}\nfunction createHooks() {\n  return new Hookable();\n}\n\nconst isBrowser = typeof window !== \"undefined\";\nfunction createDebugger(hooks, _options = {}) {\n  const options = {\n    inspect: isBrowser,\n    group: isBrowser,\n    filter: () => true,\n    ..._options\n  };\n  const _filter = options.filter;\n  const filter = typeof _filter === \"string\" ? (name) => name.startsWith(_filter) : _filter;\n  const _tag = options.tag ? `[${options.tag}] ` : \"\";\n  const logPrefix = (event) => _tag + event.name + \"\".padEnd(event._id, \"\\0\");\n  const _idCtr = {};\n  const unsubscribeBefore = hooks.beforeEach((event) => {\n    if (filter !== void 0 && !filter(event.name)) {\n      return;\n    }\n    _idCtr[event.name] = _idCtr[event.name] || 0;\n    event._id = _idCtr[event.name]++;\n    console.time(logPrefix(event));\n  });\n  const unsubscribeAfter = hooks.afterEach((event) => {\n    if (filter !== void 0 && !filter(event.name)) {\n      return;\n    }\n    if (options.group) {\n      console.groupCollapsed(event.name);\n    }\n    if (options.inspect) {\n      console.timeLog(logPrefix(event), event.args);\n    } else {\n      console.timeEnd(logPrefix(event));\n    }\n    if (options.group) {\n      console.groupEnd();\n    }\n    _idCtr[event.name]--;\n  });\n  return {\n    /** Stop debugging and remove listeners */\n    close: () => {\n      unsubscribeBefore();\n      unsubscribeAfter();\n    }\n  };\n}\n\nexport { Hookable, createDebugger, createHooks, flatHooks, mergeHooks, parallelCaller, serial, serialCaller };\n", "import { DomPlugin } from '@unhead/dom';\nimport { defineHeadPlugin, tagDedupeKey, hashTag, tagWeight, HasElementTags, NetworkEvents, hashCode, SortModifiers, processTemplateParams, resolveTitleTemplate, IsBrowser, normaliseEntryTags, composableNames, whitelistSafeInput, ScriptNetworkEvents, unpackMeta } from '@unhead/shared';\nexport { composableNames } from '@unhead/shared';\nimport { createHooks } from 'hookable';\n\nconst UsesMergeStrategy = /* @__PURE__ */ new Set([\"templateParams\", \"htmlAttrs\", \"bodyAttrs\"]);\nconst DedupePlugin = defineHeadPlugin({\n  hooks: {\n    \"tag:normalise\": ({ tag }) => {\n      if (tag.props.hid) {\n        tag.key = tag.props.hid;\n        delete tag.props.hid;\n      }\n      if (tag.props.vmid) {\n        tag.key = tag.props.vmid;\n        delete tag.props.vmid;\n      }\n      if (tag.props.key) {\n        tag.key = tag.props.key;\n        delete tag.props.key;\n      }\n      const generatedKey = tagDedupeKey(tag);\n      if (generatedKey && !generatedKey.startsWith(\"meta:og:\") && !generatedKey.startsWith(\"meta:twitter:\")) {\n        delete tag.key;\n      }\n      const dedupe = generatedKey || (tag.key ? `${tag.tag}:${tag.key}` : false);\n      if (dedupe)\n        tag._d = dedupe;\n    },\n    \"tags:resolve\": (ctx) => {\n      const deduping = /* @__PURE__ */ Object.create(null);\n      for (const tag of ctx.tags) {\n        const dedupeKey = (tag.key ? `${tag.tag}:${tag.key}` : tag._d) || hashTag(tag);\n        const dupedTag = deduping[dedupeKey];\n        if (dupedTag) {\n          let strategy = tag?.tagDuplicateStrategy;\n          if (!strategy && UsesMergeStrategy.has(tag.tag))\n            strategy = \"merge\";\n          if (strategy === \"merge\") {\n            const oldProps = dupedTag.props;\n            if (oldProps.style && tag.props.style) {\n              if (oldProps.style[oldProps.style.length - 1] !== \";\") {\n                oldProps.style += \";\";\n              }\n              tag.props.style = `${oldProps.style} ${tag.props.style}`;\n            }\n            if (oldProps.class && tag.props.class) {\n              tag.props.class = `${oldProps.class} ${tag.props.class}`;\n            } else if (oldProps.class) {\n              tag.props.class = oldProps.class;\n            }\n            deduping[dedupeKey].props = {\n              ...oldProps,\n              ...tag.props\n            };\n            continue;\n          } else if (tag._e === dupedTag._e) {\n            dupedTag._duped = dupedTag._duped || [];\n            tag._d = `${dupedTag._d}:${dupedTag._duped.length + 1}`;\n            dupedTag._duped.push(tag);\n            continue;\n          } else if (tagWeight(tag) > tagWeight(dupedTag)) {\n            continue;\n          }\n        }\n        const hasProps = tag.innerHTML || tag.textContent || Object.keys(tag.props).length !== 0;\n        if (!hasProps && HasElementTags.has(tag.tag)) {\n          delete deduping[dedupeKey];\n          continue;\n        }\n        deduping[dedupeKey] = tag;\n      }\n      const newTags = [];\n      for (const key in deduping) {\n        const tag = deduping[key];\n        const dupes = tag._duped;\n        newTags.push(tag);\n        if (dupes) {\n          delete tag._duped;\n          newTags.push(...dupes);\n        }\n      }\n      ctx.tags = newTags;\n      ctx.tags = ctx.tags.filter((t) => !(t.tag === \"meta\" && (t.props.name || t.props.property) && !t.props.content));\n    }\n  }\n});\n\nconst ValidEventTags = /* @__PURE__ */ new Set([\"script\", \"link\", \"bodyAttrs\"]);\nconst EventHandlersPlugin = defineHeadPlugin((head) => ({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (!ValidEventTags.has(tag.tag)) {\n          continue;\n        }\n        const props = tag.props;\n        for (const key in props) {\n          if (key[0] !== \"o\" || key[1] !== \"n\") {\n            continue;\n          }\n          if (!Object.prototype.hasOwnProperty.call(props, key)) {\n            continue;\n          }\n          const value = props[key];\n          if (typeof value !== \"function\") {\n            continue;\n          }\n          if (head.ssr && NetworkEvents.has(key)) {\n            props[key] = `this.dataset.${key}fired = true`;\n          } else {\n            delete props[key];\n          }\n          tag._eventHandlers = tag._eventHandlers || {};\n          tag._eventHandlers[key] = value;\n        }\n        if (head.ssr && tag._eventHandlers && (tag.props.src || tag.props.href)) {\n          tag.key = tag.key || hashCode(tag.props.src || tag.props.href);\n        }\n      }\n    },\n    \"dom:renderTag\": ({ $el, tag }) => {\n      const dataset = $el?.dataset;\n      if (!dataset) {\n        return;\n      }\n      for (const k in dataset) {\n        if (!k.endsWith(\"fired\")) {\n          continue;\n        }\n        const ek = k.slice(0, -5);\n        if (!NetworkEvents.has(ek)) {\n          continue;\n        }\n        tag._eventHandlers?.[ek]?.call($el, new Event(ek.substring(2)));\n      }\n    }\n  }\n}));\n\nconst DupeableTags = /* @__PURE__ */ new Set([\"link\", \"style\", \"script\", \"noscript\"]);\nconst HashKeyedPlugin = defineHeadPlugin({\n  hooks: {\n    \"tag:normalise\": ({ tag }) => {\n      if (tag.key && DupeableTags.has(tag.tag)) {\n        tag.props[\"data-hid\"] = tag._h = hashCode(tag.key);\n      }\n    }\n  }\n});\n\nconst PayloadPlugin = defineHeadPlugin({\n  mode: \"server\",\n  hooks: {\n    \"tags:beforeResolve\": (ctx) => {\n      const payload = {};\n      let hasPayload = false;\n      for (const tag of ctx.tags) {\n        if (tag._m !== \"server\" || tag.tag !== \"titleTemplate\" && tag.tag !== \"templateParams\" && tag.tag !== \"title\") {\n          continue;\n        }\n        payload[tag.tag] = tag.tag === \"title\" || tag.tag === \"titleTemplate\" ? tag.textContent : tag.props;\n        hasPayload = true;\n      }\n      if (hasPayload) {\n        ctx.tags.push({\n          tag: \"script\",\n          innerHTML: JSON.stringify(payload),\n          props: { id: \"unhead:payload\", type: \"application/json\" }\n        });\n      }\n    }\n  }\n});\n\nconst SortPlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (typeof tag.tagPriority !== \"string\") {\n          continue;\n        }\n        for (const { prefix, offset } of SortModifiers) {\n          if (!tag.tagPriority.startsWith(prefix)) {\n            continue;\n          }\n          const key = tag.tagPriority.substring(prefix.length);\n          const position = ctx.tags.find((tag2) => tag2._d === key)?._p;\n          if (position !== void 0) {\n            tag._p = position + offset;\n            break;\n          }\n        }\n      }\n      ctx.tags.sort((a, b) => {\n        const aWeight = tagWeight(a);\n        const bWeight = tagWeight(b);\n        if (aWeight < bWeight) {\n          return -1;\n        } else if (aWeight > bWeight) {\n          return 1;\n        }\n        return a._p - b._p;\n      });\n    }\n  }\n});\n\nconst SupportedAttrs = {\n  meta: \"content\",\n  link: \"href\",\n  htmlAttrs: \"lang\"\n};\nconst contentAttrs = [\"innerHTML\", \"textContent\"];\nconst TemplateParamsPlugin = defineHeadPlugin((head) => ({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      const { tags } = ctx;\n      let templateParams;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag !== \"templateParams\") {\n          continue;\n        }\n        templateParams = ctx.tags.splice(i, 1)[0].props;\n        i -= 1;\n      }\n      const params = templateParams || {};\n      const sep = params.separator || \"|\";\n      delete params.separator;\n      params.pageTitle = processTemplateParams(\n        // find templateParams\n        params.pageTitle || tags.find((tag) => tag.tag === \"title\")?.textContent || \"\",\n        params,\n        sep\n      );\n      for (const tag of tags) {\n        if (tag.processTemplateParams === false) {\n          continue;\n        }\n        const v = SupportedAttrs[tag.tag];\n        if (v && typeof tag.props[v] === \"string\") {\n          tag.props[v] = processTemplateParams(tag.props[v], params, sep);\n        } else if (tag.processTemplateParams || tag.tag === \"titleTemplate\" || tag.tag === \"title\") {\n          for (const p of contentAttrs) {\n            if (typeof tag[p] === \"string\")\n              tag[p] = processTemplateParams(tag[p], params, sep, tag.tag === \"script\" && tag.props.type.endsWith(\"json\"));\n          }\n        }\n      }\n      head._templateParams = params;\n      head._separator = sep;\n    },\n    \"tags:afterResolve\": ({ tags }) => {\n      let title;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag === \"title\" && tag.processTemplateParams !== false) {\n          title = tag;\n        }\n      }\n      if (title?.textContent) {\n        title.textContent = processTemplateParams(title.textContent, head._templateParams, head._separator);\n      }\n    }\n  }\n}));\n\nconst TitleTemplatePlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      const { tags } = ctx;\n      let titleTag;\n      let titleTemplateTag;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag === \"title\") {\n          titleTag = tag;\n        } else if (tag.tag === \"titleTemplate\") {\n          titleTemplateTag = tag;\n        }\n      }\n      if (titleTemplateTag && titleTag) {\n        const newTitle = resolveTitleTemplate(\n          titleTemplateTag.textContent,\n          titleTag.textContent\n        );\n        if (newTitle !== null) {\n          titleTag.textContent = newTitle || titleTag.textContent;\n        } else {\n          ctx.tags.splice(ctx.tags.indexOf(titleTag), 1);\n        }\n      } else if (titleTemplateTag) {\n        const newTitle = resolveTitleTemplate(\n          titleTemplateTag.textContent\n        );\n        if (newTitle !== null) {\n          titleTemplateTag.textContent = newTitle;\n          titleTemplateTag.tag = \"title\";\n          titleTemplateTag = void 0;\n        }\n      }\n      if (titleTemplateTag) {\n        ctx.tags.splice(ctx.tags.indexOf(titleTemplateTag), 1);\n      }\n    }\n  }\n});\n\nconst XSSPlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:afterResolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (typeof tag.innerHTML === \"string\") {\n          if (tag.innerHTML && (tag.props.type === \"application/ld+json\" || tag.props.type === \"application/json\")) {\n            tag.innerHTML = tag.innerHTML.replace(/</g, \"\\\\u003C\");\n          } else {\n            tag.innerHTML = tag.innerHTML.replace(new RegExp(`</${tag.tag}`, \"g\"), `<\\\\/${tag.tag}`);\n          }\n        }\n      }\n    }\n  }\n});\n\nlet activeHead;\n// @__NO_SIDE_EFFECTS__\nfunction createHead(options = {}) {\n  const head = createHeadCore(options);\n  head.use(DomPlugin());\n  return activeHead = head;\n}\n// @__NO_SIDE_EFFECTS__\nfunction createServerHead(options = {}) {\n  return activeHead = createHeadCore(options);\n}\nfunction filterMode(mode, ssr) {\n  return !mode || mode === \"server\" && ssr || mode === \"client\" && !ssr;\n}\nfunction createHeadCore(options = {}) {\n  const hooks = createHooks();\n  hooks.addHooks(options.hooks || {});\n  options.document = options.document || (IsBrowser ? document : void 0);\n  const ssr = !options.document;\n  const updated = () => {\n    head.dirty = true;\n    hooks.callHook(\"entries:updated\", head);\n  };\n  let entryCount = 0;\n  let entries = [];\n  const plugins = [];\n  const head = {\n    plugins,\n    dirty: false,\n    resolvedOptions: options,\n    hooks,\n    headEntries() {\n      return entries;\n    },\n    use(p) {\n      const plugin = typeof p === \"function\" ? p(head) : p;\n      if (!plugin.key || !plugins.some((p2) => p2.key === plugin.key)) {\n        plugins.push(plugin);\n        filterMode(plugin.mode, ssr) && hooks.addHooks(plugin.hooks || {});\n      }\n    },\n    push(input, entryOptions) {\n      delete entryOptions?.head;\n      const entry = {\n        _i: entryCount++,\n        input,\n        ...entryOptions\n      };\n      if (filterMode(entry.mode, ssr)) {\n        entries.push(entry);\n        updated();\n      }\n      return {\n        dispose() {\n          entries = entries.filter((e) => e._i !== entry._i);\n          updated();\n        },\n        // a patch is the same as creating a new entry, just a nice DX\n        patch(input2) {\n          for (const e of entries) {\n            if (e._i === entry._i) {\n              e.input = entry.input = input2;\n            }\n          }\n          updated();\n        }\n      };\n    },\n    async resolveTags() {\n      const resolveCtx = { tags: [], entries: [...entries] };\n      await hooks.callHook(\"entries:resolve\", resolveCtx);\n      for (const entry of resolveCtx.entries) {\n        const resolved = entry.resolvedInput || entry.input;\n        entry.resolvedInput = await (entry.transform ? entry.transform(resolved) : resolved);\n        if (entry.resolvedInput) {\n          for (const tag of await normaliseEntryTags(entry)) {\n            const tagCtx = { tag, entry, resolvedOptions: head.resolvedOptions };\n            await hooks.callHook(\"tag:normalise\", tagCtx);\n            resolveCtx.tags.push(tagCtx.tag);\n          }\n        }\n      }\n      await hooks.callHook(\"tags:beforeResolve\", resolveCtx);\n      await hooks.callHook(\"tags:resolve\", resolveCtx);\n      await hooks.callHook(\"tags:afterResolve\", resolveCtx);\n      return resolveCtx.tags;\n    },\n    ssr\n  };\n  [\n    DedupePlugin,\n    PayloadPlugin,\n    EventHandlersPlugin,\n    HashKeyedPlugin,\n    SortPlugin,\n    TemplateParamsPlugin,\n    TitleTemplatePlugin,\n    XSSPlugin,\n    ...options?.plugins || []\n  ].forEach((p) => head.use(p));\n  head.hooks.callHook(\"init\", head);\n  return head;\n}\n\nconst unheadComposablesImports = [\n  {\n    from: \"unhead\",\n    imports: composableNames\n  }\n];\n\nfunction getActiveHead() {\n  return activeHead;\n}\n\nfunction useHead(input, options = {}) {\n  const head = options.head || getActiveHead();\n  return head?.push(input, options);\n}\n\nfunction useHeadSafe(input, options) {\n  return useHead(input, {\n    ...options,\n    transform: whitelistSafeInput\n  });\n}\n\nconst ScriptProxyTarget = Symbol(\"ScriptProxyTarget\");\nfunction scriptProxy() {\n}\nscriptProxy[ScriptProxyTarget] = true;\nfunction resolveScriptKey(input) {\n  return input.key || hashCode(input.src || (typeof input.innerHTML === \"string\" ? input.innerHTML : \"\"));\n}\nfunction useScript(_input, _options) {\n  const input = typeof _input === \"string\" ? { src: _input } : _input;\n  const options = _options || {};\n  const head = options.head || getActiveHead();\n  if (!head)\n    throw new Error(\"Missing Unhead context.\");\n  const id = resolveScriptKey(input);\n  const prevScript = head._scripts?.[id];\n  if (prevScript) {\n    prevScript.setupTriggerHandler(options.trigger);\n    return prevScript;\n  }\n  options.beforeInit?.();\n  const syncStatus = (s) => {\n    script.status = s;\n    head.hooks.callHook(`script:updated`, hookCtx);\n  };\n  ScriptNetworkEvents.forEach((fn) => {\n    const _fn = typeof input[fn] === \"function\" ? input[fn].bind(options.eventContext) : null;\n    input[fn] = (e) => {\n      syncStatus(fn === \"onload\" ? \"loaded\" : fn === \"onerror\" ? \"error\" : \"loading\");\n      _fn?.(e);\n    };\n  });\n  const _cbs = { loaded: [], error: [] };\n  const _registerCb = (key, cb) => {\n    if (_cbs[key]) {\n      const i = _cbs[key].push(cb);\n      return () => _cbs[key]?.splice(i - 1, 1);\n    }\n    cb(script.instance);\n    return () => {\n    };\n  };\n  const loadPromise = new Promise((resolve) => {\n    if (head.ssr)\n      return;\n    const emit = (api) => requestAnimationFrame(() => resolve(api));\n    const _ = head.hooks.hook(\"script:updated\", ({ script: script2 }) => {\n      const status = script2.status;\n      if (script2.id === id && (status === \"loaded\" || status === \"error\")) {\n        if (status === \"loaded\") {\n          if (typeof options.use === \"function\") {\n            const api = options.use();\n            if (api) {\n              emit(api);\n            }\n          } else {\n            emit({});\n          }\n        } else if (status === \"error\") {\n          resolve(false);\n        }\n        _();\n      }\n    });\n  });\n  const script = Object.assign(loadPromise, {\n    instance: !head.ssr && options?.use?.() || null,\n    proxy: null,\n    id,\n    status: \"awaitingLoad\",\n    remove() {\n      script._triggerAbortController?.abort();\n      script._triggerPromises = [];\n      if (script.entry) {\n        script.entry.dispose();\n        script.entry = void 0;\n        syncStatus(\"removed\");\n        delete head._scripts?.[id];\n        return true;\n      }\n      return false;\n    },\n    load(cb) {\n      script._triggerAbortController?.abort();\n      script._triggerPromises = [];\n      if (!script.entry) {\n        syncStatus(\"loading\");\n        const defaults = {\n          defer: true,\n          fetchpriority: \"low\"\n        };\n        if (input.src && (input.src.startsWith(\"http\") || input.src.startsWith(\"//\"))) {\n          defaults.crossorigin = \"anonymous\";\n          defaults.referrerpolicy = \"no-referrer\";\n        }\n        script.entry = head.push({\n          script: [{ ...defaults, ...input, key: `script.${id}` }]\n        }, options);\n      }\n      if (cb)\n        _registerCb(\"loaded\", cb);\n      return loadPromise;\n    },\n    onLoaded(cb) {\n      return _registerCb(\"loaded\", cb);\n    },\n    onError(cb) {\n      return _registerCb(\"error\", cb);\n    },\n    setupTriggerHandler(trigger) {\n      if (script.status !== \"awaitingLoad\") {\n        return;\n      }\n      if ((typeof trigger === \"undefined\" || trigger === \"client\") && !head.ssr || trigger === \"server\") {\n        script.load();\n      } else if (trigger instanceof Promise) {\n        if (head.ssr) {\n          return;\n        }\n        if (!script._triggerAbortController) {\n          script._triggerAbortController = new AbortController();\n          script._triggerAbortPromise = new Promise((resolve) => {\n            script._triggerAbortController.signal.addEventListener(\"abort\", () => {\n              script._triggerAbortController = null;\n              resolve();\n            });\n          });\n        }\n        script._triggerPromises = script._triggerPromises || [];\n        const idx = script._triggerPromises.push(Promise.race([\n          trigger.then((v) => typeof v === \"undefined\" || v ? script.load : void 0),\n          script._triggerAbortPromise\n        ]).catch(() => {\n        }).then((res2) => {\n          res2?.();\n        }).finally(() => {\n          script._triggerPromises?.splice(idx, 1);\n        }));\n      } else if (typeof trigger === \"function\") {\n        trigger(script.load);\n      }\n    },\n    _cbs\n  });\n  loadPromise.then((api) => {\n    if (api !== false) {\n      script.instance = api;\n      _cbs.loaded?.forEach((cb) => cb(api));\n      _cbs.loaded = null;\n    } else {\n      _cbs.error?.forEach((cb) => cb());\n      _cbs.error = null;\n    }\n  });\n  const hookCtx = { script };\n  script.setupTriggerHandler(options.trigger);\n  script.$script = script;\n  const proxyChain = (instance, accessor, accessors) => {\n    return new Proxy((!accessor ? instance : instance?.[accessor]) || scriptProxy, {\n      get(_, k, r) {\n        head.hooks.callHook(\"script:instance-fn\", { script, fn: k, exists: k in _ });\n        if (!accessor) {\n          const stub = options.stub?.({ script, fn: k });\n          if (stub)\n            return stub;\n        }\n        if (_ && k in _ && typeof _[k] !== \"undefined\") {\n          return Reflect.get(_, k, r);\n        }\n        if (k === Symbol.iterator) {\n          return [][Symbol.iterator];\n        }\n        return proxyChain(accessor ? instance?.[accessor] : instance, k, accessors || [k]);\n      },\n      async apply(_, _this, args) {\n        if (head.ssr && _[ScriptProxyTarget])\n          return;\n        let instance2;\n        const access = (fn2) => {\n          instance2 = fn2 || instance2;\n          for (let i = 0; i < (accessors || []).length; i++) {\n            const k = (accessors || [])[i];\n            fn2 = fn2?.[k];\n          }\n          return fn2;\n        };\n        let fn = access(script.instance);\n        if (!fn) {\n          fn = await new Promise((resolve) => {\n            script.onLoaded((api) => {\n              resolve(access(api));\n            });\n          });\n        }\n        return typeof fn === \"function\" ? Reflect.apply(fn, instance2, args) : fn;\n      }\n    });\n  };\n  script.proxy = proxyChain(script.instance);\n  const res = new Proxy(script, {\n    get(_, k) {\n      const target = k in script || String(k)[0] === \"_\" ? script : script.proxy;\n      if (k === \"then\" || k === \"catch\") {\n        return script[k].bind(script);\n      }\n      return Reflect.get(target, k, target);\n    }\n  });\n  head._scripts = Object.assign(head._scripts || {}, { [id]: res });\n  return res;\n}\n\nfunction useSeoMeta(input, options) {\n  const { title, titleTemplate, ...meta } = input;\n  return useHead({\n    title,\n    titleTemplate,\n    // we need to input the meta so the reactivity will be resolved\n    // @ts-expect-error runtime type\n    _flatMeta: meta\n  }, {\n    ...options,\n    transform(t) {\n      const meta2 = unpackMeta({ ...t._flatMeta });\n      delete t._flatMeta;\n      return {\n        // @ts-expect-error runtime type\n        ...t,\n        meta: meta2\n      };\n    }\n  });\n}\n\nfunction useServerHead(input, options = {}) {\n  return useHead(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerHeadSafe(input, options = {}) {\n  return useHeadSafe(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerSeoMeta(input, options) {\n  return useSeoMeta(input, {\n    ...options,\n    mode: \"server\"\n  });\n}\n\nconst importRe = /@import/;\n// @__NO_SIDE_EFFECTS__\nfunction CapoPlugin(options) {\n  return defineHeadPlugin({\n    hooks: {\n      \"tags:beforeResolve\": ({ tags }) => {\n        for (const tag of tags) {\n          if (tag.tagPosition && tag.tagPosition !== \"head\")\n            continue;\n          tag.tagPriority = tag.tagPriority || tagWeight(tag);\n          if (tag.tagPriority !== 100)\n            continue;\n          const isTruthy = (val) => val === \"\" || val === true;\n          const isScript = tag.tag === \"script\";\n          const isLink = tag.tag === \"link\";\n          if (isScript && isTruthy(tag.props.async)) {\n            tag.tagPriority = 30;\n          } else if (tag.tag === \"style\" && tag.innerHTML && importRe.test(tag.innerHTML)) {\n            tag.tagPriority = 40;\n          } else if (isScript && tag.props.src && !isTruthy(tag.props.defer) && !isTruthy(tag.props.async) && tag.props.type !== \"module\" && !tag.props.type?.endsWith(\"json\")) {\n            tag.tagPriority = 50;\n          } else if (isLink && tag.props.rel === \"stylesheet\" || tag.tag === \"style\") {\n            tag.tagPriority = 60;\n          } else if (isLink && (tag.props.rel === \"preload\" || tag.props.rel === \"modulepreload\")) {\n            tag.tagPriority = 70;\n          } else if (isScript && isTruthy(tag.props.defer) && tag.props.src && !isTruthy(tag.props.async)) {\n            tag.tagPriority = 80;\n          } else if (isLink && (tag.props.rel === \"prefetch\" || tag.props.rel === \"dns-prefetch\" || tag.props.rel === \"prerender\")) {\n            tag.tagPriority = 90;\n          }\n        }\n        options?.track && tags.push({\n          tag: \"htmlAttrs\",\n          props: {\n            \"data-capo\": \"\"\n          }\n        });\n      }\n    }\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction HashHydrationPlugin() {\n  return defineHeadPlugin({});\n}\n\nexport { CapoPlugin, HashHydrationPlugin, createHead, createHeadCore, createServerHead, getActiveHead, resolveScriptKey, unheadComposablesImports, useHead, useHeadSafe, useScript, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };\n", "import { createServerHead as createServerHead$1, createHead as createHead$1, getActiveHead } from 'unhead';\nimport { version, unref, nextTick, inject } from 'vue';\nimport { defineHeadPlugin } from '@unhead/shared';\n\nconst Vue3 = version[0] === \"3\";\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\nfunction resolveUnrefHeadInput(ref) {\n  if (ref instanceof Promise || ref instanceof Date || ref instanceof RegExp)\n    return ref;\n  const root = resolveUnref(ref);\n  if (!ref || !root)\n    return root;\n  if (Array.isArray(root))\n    return root.map((r) => resolveUnrefHeadInput(r));\n  if (typeof root === \"object\") {\n    const resolved = {};\n    for (const k in root) {\n      if (!Object.prototype.hasOwnProperty.call(root, k)) {\n        continue;\n      }\n      if (k === \"titleTemplate\" || k[0] === \"o\" && k[1] === \"n\") {\n        resolved[k] = unref(root[k]);\n        continue;\n      }\n      resolved[k] = resolveUnrefHeadInput(root[k]);\n    }\n    return resolved;\n  }\n  return root;\n}\n\nconst VueReactivityPlugin = defineHeadPlugin({\n  hooks: {\n    \"entries:resolve\": (ctx) => {\n      for (const entry of ctx.entries)\n        entry.resolvedInput = resolveUnrefHeadInput(entry.input);\n    }\n  }\n});\n\nconst headSymbol = \"usehead\";\nfunction vueInstall(head) {\n  const plugin = {\n    install(app) {\n      if (Vue3) {\n        app.config.globalProperties.$unhead = head;\n        app.config.globalProperties.$head = head;\n        app.provide(headSymbol, head);\n      }\n    }\n  };\n  return plugin.install;\n}\nfunction createServerHead(options = {}) {\n  const head = createServerHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\nfunction createHead(options = {}) {\n  options.domDelayFn = options.domDelayFn || ((fn) => nextTick(() => setTimeout(() => fn(), 0)));\n  const head = createHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__unhead_injection_handler__\";\nfunction setHeadInjectionHandler(handler) {\n  _global[globalKey] = handler;\n}\nfunction injectHead() {\n  if (globalKey in _global) {\n    return _global[globalKey]();\n  }\n  const head = inject(headSymbol);\n  if (!head && process.env.NODE_ENV !== \"production\")\n    console.warn(\"Unhead is missing Vue context, falling back to shared context. This may have unexpected results.\");\n  return head || getActiveHead();\n}\n\nexport { Vue3 as V, createServerHead as a, createHead as c, headSymbol as h, injectHead as i, resolveUnrefHeadInput as r, setHeadInjectionHandler as s };\n", "import { useScript as useScript$1 } from 'unhead';\nexport { CapoPlugin, HashHydrationPlugin, createHeadCore } from 'unhead';\nimport { i as injectHead, h as headSymbol, V as Vue3 } from './shared/vue.ziyDaVMR.mjs';\nexport { c as createHead, a as createServerHead, r as resolveUnrefHeadInput, s as setHeadInjectionHandler } from './shared/vue.ziyDaVMR.mjs';\nimport { composableNames, whitelistSafeInput, unpackMeta } from '@unhead/shared';\nimport { u as useHead } from './shared/vue.-sixQ7xP.mjs';\nimport { getCurrentInstance, onMounted, isRef, watch, onScopeDispose, ref } from 'vue';\n\nconst coreComposableNames = [\n  \"injectHead\"\n];\nconst unheadVueComposablesImports = {\n  \"@unhead/vue\": [...coreComposableNames, ...composableNames]\n};\n\nfunction useHeadSafe(input, options = {}) {\n  return useHead(input, { ...options, transform: whitelistSafeInput });\n}\n\nfunction registerVueScopeHandlers(script, scope) {\n  if (!scope) {\n    return;\n  }\n  const _registerCb = (key, cb) => {\n    if (!script._cbs[key]) {\n      cb(script.instance);\n      return () => {\n      };\n    }\n    let i = script._cbs[key].push(cb);\n    const destroy = () => {\n      if (i) {\n        script._cbs[key]?.splice(i - 1, 1);\n        i = null;\n      }\n    };\n    onScopeDispose(destroy);\n    return destroy;\n  };\n  script.onLoaded = (cb) => _registerCb(\"loaded\", cb);\n  script.onError = (cb) => _registerCb(\"error\", cb);\n  onScopeDispose(() => {\n    script._triggerAbortController?.abort();\n  });\n}\nfunction useScript(_input, _options) {\n  const input = typeof _input === \"string\" ? { src: _input } : _input;\n  const options = _options || {};\n  const head = options?.head || injectHead();\n  options.head = head;\n  const scope = getCurrentInstance();\n  options.eventContext = scope;\n  if (scope && typeof options.trigger === \"undefined\") {\n    options.trigger = onMounted;\n  } else if (isRef(options.trigger)) {\n    const refTrigger = options.trigger;\n    let off;\n    options.trigger = new Promise((resolve) => {\n      off = watch(refTrigger, (val) => {\n        if (val) {\n          resolve(true);\n        }\n      }, {\n        immediate: true\n      });\n      onScopeDispose(() => resolve(false), true);\n    }).then((val) => {\n      off?.();\n      return val;\n    });\n  }\n  head._scriptStatusWatcher = head._scriptStatusWatcher || head.hooks.hook(\"script:updated\", ({ script: s }) => {\n    s._statusRef.value = s.status;\n  });\n  const script = useScript$1(input, options);\n  script._statusRef = script._statusRef || ref(script.status);\n  registerVueScopeHandlers(script, scope);\n  return new Proxy(script, {\n    get(_, key, a) {\n      return Reflect.get(_, key === \"status\" ? \"_statusRef\" : key, a);\n    }\n  });\n}\n\nfunction useSeoMeta(input, options) {\n  const { title, titleTemplate, ...meta } = input;\n  return useHead({\n    title,\n    titleTemplate,\n    // @ts-expect-error runtime type\n    _flatMeta: meta\n  }, {\n    ...options,\n    transform(t) {\n      const meta2 = unpackMeta({ ...t._flatMeta });\n      delete t._flatMeta;\n      return {\n        // @ts-expect-error runtime type\n        ...t,\n        meta: meta2\n      };\n    }\n  });\n}\n\nfunction useServerHead(input, options = {}) {\n  const head = options.head || injectHead();\n  delete options.head;\n  if (head)\n    return head.push(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerHeadSafe(input, options = {}) {\n  return useHeadSafe(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerSeoMeta(input, options) {\n  return useSeoMeta(input, { ...options, mode: \"server\" });\n}\n\nconst Vue2ProvideUnheadPlugin = (_Vue, head) => {\n  _Vue.mixin({\n    beforeCreate() {\n      const options = this.$options;\n      const origProvide = options.provide;\n      options.provide = function() {\n        let origProvideResult;\n        if (typeof origProvide === \"function\")\n          origProvideResult = origProvide.call(this);\n        else\n          origProvideResult = origProvide || {};\n        return {\n          ...origProvideResult,\n          [headSymbol]: head\n        };\n      };\n    }\n  });\n};\n\nconst VueHeadMixin = {\n  created() {\n    let source = false;\n    if (Vue3) {\n      const instance = getCurrentInstance();\n      if (!instance)\n        return;\n      const options = instance.type;\n      if (!options || !(\"head\" in options))\n        return;\n      source = typeof options.head === \"function\" ? () => options.head.call(instance.proxy) : options.head;\n    } else {\n      const head = this.$options.head;\n      if (head) {\n        source = typeof head === \"function\" ? () => head.call(this) : head;\n      }\n    }\n    source && useHead(source);\n  }\n};\n\nexport { Vue2ProvideUnheadPlugin, VueHeadMixin, injectHead, unheadVueComposablesImports, useHead, useHeadSafe, useScript, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };\n", "import { defineComponent, ref, onMounted } from 'vue';\n\nfunction documentReady(_passThrough) {\n  if (document.readyState === \"loading\") {\n    return new Promise((resolve) => {\n      document.addEventListener(\"DOMContentLoaded\", () => resolve(_passThrough));\n    });\n  }\n  return Promise.resolve(_passThrough);\n}\n\nconst ClientOnly = defineComponent({\n  setup(props, { slots }) {\n    const mounted = ref(false);\n    onMounted(() => mounted.value = true);\n    return () => {\n      if (!mounted.value)\n        return slots.placeholder && slots.placeholder({});\n      return slots.default && slots.default({});\n    };\n  }\n});\n\nexport { ClientOnly as C, documentReady as d };\n", "const UNSAFE_CHARS_REGEXP = /[<>/\\u2028\\u2029]/g;\nconst ESCAPED_CHARS = {\n  \"<\": \"\\\\u003C\",\n  \">\": \"\\\\u003E\",\n  \"/\": \"\\\\u002F\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nfunction escapeUnsafeChars(unsafeChar) {\n  return ESCAPED_CHARS[unsafeChar];\n}\nfunction serializeState(state) {\n  if (state == null || Object.keys(state).length === 0)\n    return null;\n  try {\n    return JSON.stringify(JSON.stringify(state || {})).replace(\n      UNSAFE_CHARS_REGEXP,\n      escapeUnsafeChars\n    );\n  } catch (error) {\n    console.error(\"[SSG] On state serialization -\", error, state);\n    return null;\n  }\n}\nfunction deserializeState(state) {\n  try {\n    return JSON.parse(state || \"{}\");\n  } catch (error) {\n    console.error(\"[SSG] On state deserialization -\", error, state);\n    return {};\n  }\n}\n\nexport { deserializeState as d, serializeState as s };\n", "import { createHead } from '@unhead/vue';\nimport { createApp, createSSRApp } from 'vue';\nimport { createRouter, createWebHistory, createMemoryHistory } from 'vue-router';\nimport { C as ClientOnly, d as documentReady } from './shared/vite-ssg.ETIvV-80.mjs';\nimport { d as deserializeState } from './shared/vite-ssg.C6pK7rvr.mjs';\n\nfunction ViteSSG(App, routerOptions, fn, options = {}) {\n  const {\n    transformState,\n    registerComponents = true,\n    useHead = true,\n    rootContainer = \"#app\"\n  } = options;\n  const isClient = typeof window !== \"undefined\";\n  async function createApp$1(client = false, routePath) {\n    const app = client ? createApp(App) : createSSRApp(App);\n    let head;\n    if (useHead) {\n      head = createHead();\n      app.use(head);\n    }\n    const router = createRouter({\n      history: client ? createWebHistory(routerOptions.base) : createMemoryHistory(routerOptions.base),\n      ...routerOptions\n    });\n    const { routes } = routerOptions;\n    if (registerComponents)\n      app.component(\"ClientOnly\", ClientOnly);\n    const appRenderCallbacks = [];\n    const onSSRAppRendered = client ? () => {\n    } : (cb) => appRenderCallbacks.push(cb);\n    const triggerOnSSRAppRendered = () => {\n      return Promise.all(appRenderCallbacks.map((cb) => cb()));\n    };\n    const context = {\n      app,\n      head,\n      isClient,\n      router,\n      routes,\n      onSSRAppRendered,\n      triggerOnSSRAppRendered,\n      initialState: {},\n      transformState,\n      routePath\n    };\n    if (client) {\n      await documentReady();\n      context.initialState = transformState?.(window.__INITIAL_STATE__ || {}) || deserializeState(window.__INITIAL_STATE__);\n    }\n    await fn?.(context);\n    app.use(router);\n    let entryRoutePath;\n    let isFirstRoute = true;\n    router.beforeEach((to, from, next) => {\n      if (isFirstRoute || entryRoutePath && entryRoutePath === to.path) {\n        isFirstRoute = false;\n        entryRoutePath = to.path;\n        to.meta.state = context.initialState;\n      }\n      next();\n    });\n    if (!client) {\n      const route = context.routePath ?? \"/\";\n      router.push(route);\n      await router.isReady();\n      context.initialState = router.currentRoute.value.meta.state || {};\n    }\n    const initialState = context.initialState;\n    return {\n      ...context,\n      initialState\n    };\n  }\n  if (isClient) {\n    (async () => {\n      const { app, router } = await createApp$1(true);\n      await router.isReady();\n      app.mount(rootContainer, true);\n    })();\n  }\n  return createApp$1;\n}\n\nexport { ViteSSG };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,IAAM,uBAAuC,oBAAI,IAAI,CAAC,SAAS,iBAAiB,UAAU,SAAS,UAAU,CAAC;AAC9G,IAAM,iBAAiC,oBAAI,IAAI;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,gBAAgC,oBAAI,IAAI;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,aAA6B,oBAAI,IAAI,CAAC,QAAQ,SAAS,iBAAiB,aAAa,aAAa,gBAAgB,CAAC;AACzH,IAAM,gBAAgC,oBAAI,IAAI,CAAC,eAAe,eAAe,wBAAwB,YAAY,aAAa,eAAe,uBAAuB,CAAC;AACrK,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,iBAAiB,QAAQ;AAChC,SAAO;AACT;AAEA,SAAS,SAAS,GAAG;AACnB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE;AACpB,QAAI,KAAK,KAAK,IAAI,EAAE,WAAW,GAAG,GAAG,KAAK,CAAC;AAC7C,WAAS,IAAI,MAAM,KAAK,OAAO,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC,EAAE,YAAY;AAC1E;AACA,SAAS,QAAQ,KAAK;AACpB,MAAI,IAAI,IAAI;AACV,WAAO,IAAI;AAAA,EACb;AACA,MAAI,IAAI,IAAI;AACV,WAAO,SAAS,IAAI,EAAE;AAAA,EACxB;AACA,MAAI,UAAU,GAAG,IAAI,GAAG,IAAI,IAAI,eAAe,IAAI,aAAa,EAAE;AAClE,aAAW,OAAO,IAAI,OAAO;AAC3B,eAAW,GAAG,GAAG,IAAI,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,EAC7C;AACA,SAAO,SAAS,OAAO;AACzB;AAEA,IAAM,IAAI,CAAC,QAAQ,EAAE,UAAU,IAAI,SAAS,WAAW;AACvD,IAAM,IAAI,CAAC,QAAQ,EAAE,UAAU,GAAG;AAClC,IAAM,oBAAoB;AAAA,EACxB,gBAAgB;AAAA,IACd,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,eAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,EAAE,yBAAyB;AAAA,EAClD,qBAAqB,EAAE,uBAAuB;AAAA,EAC9C,sBAAsB,EAAE,wBAAwB;AAAA,EAChD,iBAAiB,EAAE,mBAAmB;AAAA,EACtC,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,eAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,SAAS,EAAE,WAAW;AAAA,EACtB,qBAAqB,EAAE,sBAAsB;AAAA,EAC7C,wBAAwB,EAAE,yBAAyB;AAAA,EACnD,wBAAwB,EAAE,yBAAyB;AAAA,EACnD,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,YAAY,EAAE,cAAc;AAAA,EAC5B,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,kBAAkB,EAAE,oBAAoB;AAAA,EACxC,iBAAiB,EAAE,mBAAmB;AAAA,EACtC,iBAAiB,EAAE,kBAAkB;AAAA,EACrC,SAAS;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,YAAI,QAAQ;AACV,iBAAO,GAAG,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,YAAI,OAAO,UAAU;AACnB,iBAAO,GAAG,WAAW,GAAG,CAAC;AAAA;AAEzB,iBAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsC,oBAAI,IAAI;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAWD,SAAS,WAAW,KAAK;AACvB,QAAM,UAAU,IAAI,QAAQ,YAAY,KAAK,EAAE,YAAY;AAC3D,QAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,QAAM,OAAO,QAAQ,UAAU,GAAG,WAAW;AAC7C,MAAI,SAAS,aAAa,oBAAoB,IAAI,IAAI;AACpD,WAAO,IAAI,QAAQ,YAAY,KAAK,EAAE,YAAY;AACpD,SAAO;AACT;AA8HA,SAAS,SAAS,KAAK,QAAQ;AAC7B,MAAI,eAAe,SAAS;AAC1B,WAAO,IAAI,KAAK,MAAM;AAAA,EACxB;AACA,SAAO,OAAO,GAAG;AACnB;AAEA,SAAS,aAAa,SAAS,OAAO,GAAG,iBAAiB;AACxD,QAAM,QAAQ,mBAAmB;AAAA;AAAA;AAAA,IAG/B,OAAO,UAAU,YAAY,OAAO,UAAU,cAAc,EAAE,iBAAiB,WAAW,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC,YAAY,YAAY,YAAY,cAAc,YAAY,UAAU,cAAc,aAAa,GAAG,MAAM;AAAA,IACxN,YAAY,oBAAoB,YAAY;AAAA,EAC9C;AACA,MAAI,iBAAiB,SAAS;AAC5B,WAAO,MAAM,KAAK,CAAC,QAAQ,aAAa,SAAS,OAAO,GAAG,GAAG,CAAC;AAAA,EACjE;AACA,QAAM,MAAM;AAAA,IACV,KAAK;AAAA,IACL;AAAA,EACF;AACA,aAAWA,MAAK,eAAe;AAC7B,UAAM,MAAM,IAAI,MAAMA,EAAC,MAAM,SAAS,IAAI,MAAMA,EAAC,IAAI,EAAEA,EAAC;AACxD,QAAI,QAAQ,QAAQ;AAClB,UAAI,EAAEA,OAAM,eAAeA,OAAM,iBAAiBA,OAAM,eAAe,qBAAqB,IAAI,IAAI,GAAG,GAAG;AACxG,YAAIA,OAAM,aAAa,cAAcA,EAAC,IAAI;AAAA,MAC5C;AACA,aAAO,IAAI,MAAMA,EAAC;AAAA,IACpB;AAAA,EACF;AACA,MAAI,IAAI,MAAM,MAAM;AAClB,QAAI,cAAc;AAClB,WAAO,IAAI,MAAM;AAAA,EACnB;AACA,MAAI,IAAI,QAAQ,UAAU;AACxB,QAAI,OAAO,IAAI,cAAc,UAAU;AACrC,UAAI,YAAY,KAAK,UAAU,IAAI,SAAS;AAC5C,UAAI,MAAM,OAAO,IAAI,MAAM,QAAQ;AAAA,IACrC;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,IAAI,MAAM,QAAQ,IAAI,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,IAAI,OAAO,SAAS,EAAE,EAAE,EAAE,IAAI;AAC9H;AACA,SAAS,yBAAyB,KAAK,GAAG;AAzU1C;AA0UE,QAAM,MAAM,QAAQ,UAAU,MAAM;AACpC,MAAI,KAAK,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AACnD,QAAI,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAACA,IAAG,EAAE,MAAM,QAAQ,UAAU,GAAGA,EAAC,IAAI,EAAE,KAAKA,EAAC;AAAA,EAClG;AACA,UAAO,YAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,MAAzC,mBAA4C,MAAM,KAAK,OAAO,CAAC,MAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,KAAK;AACtG;AACA,SAAS,qBAAqB,OAAO,SAAS,MAAM,YAAY;AAC9D,WAAS,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AAChD,UAAMA,KAAI,KAAK,CAAC;AAChB,QAAIA,OAAM,WAAWA,OAAM,SAAS;AAClC,YAAMA,EAAC,IAAI,yBAAyBA,IAAG,MAAMA,EAAC,CAAC;AAC/C;AAAA,IACF;AACA,QAAI,MAAMA,EAAC,aAAa,SAAS;AAC/B,aAAO,MAAMA,EAAC,EAAE,KAAK,CAAC,QAAQ;AAC5B,cAAMA,EAAC,IAAI;AACX,eAAO,qBAAqB,OAAO,SAAS,MAAM,CAAC;AAAA,MACrD,CAAC;AAAA,IACH;AACA,QAAI,CAAC,WAAW,CAAC,cAAc,IAAIA,EAAC,GAAG;AACrC,YAAM,IAAI,OAAO,MAAMA,EAAC,CAAC;AACzB,YAAM,YAAYA,GAAE,WAAW,OAAO;AACtC,UAAI,MAAM,UAAU,MAAM,IAAI;AAC5B,cAAMA,EAAC,IAAI,YAAY,SAAS;AAAA,MAClC,WAAW,CAAC,MAAMA,EAAC,GAAG;AACpB,YAAI,aAAa,MAAM;AACrB,gBAAMA,EAAC,IAAI;AAAA;AAEX,iBAAO,MAAMA,EAAC;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,eAAe,OAAO,UAAU,OAAO;AAC9C,QAAM,gBAAgB,qBAAqB,OAAO,SAAS,OAAO,KAAK,KAAK,GAAG,CAAC;AAChF,MAAI,yBAAyB,SAAS;AACpC,WAAO,cAAc,KAAK,MAAM,KAAK;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAM,gBAAgB;AACtB,SAAS,yBAAyB,UAAU,aAAa,YAAY;AACnE,WAAS,IAAI,YAAY,IAAI,YAAY,QAAQ,KAAK,GAAG;AACvD,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,gBAAgB,SAAS;AAC3B,aAAO,KAAK,KAAK,CAAC,QAAQ;AACxB,oBAAY,CAAC,IAAI;AACjB,eAAO,yBAAyB,UAAU,aAAa,CAAC;AAAA,MAC1D,CAAC;AAAA,IACH;AACA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,eAAS,KAAK,GAAG,IAAI;AAAA,IACvB,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,GAAG;AAC7B,QAAM,cAAc,CAAC;AACrB,QAAM,QAAQ,EAAE;AAChB,aAAWA,MAAK,OAAO;AACrB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAOA,EAAC,GAAG;AACnD;AAAA,IACF;AACA,UAAM,IAAI,MAAMA,EAAC;AACjB,QAAI,MAAM,UAAU,CAAC,cAAc,IAAIA,EAAC,GAAG;AACzC;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,iBAAW,SAAS,GAAG;AACrB,oBAAY,KAAK,aAAaA,IAAG,OAAO,CAAC,CAAC;AAAA,MAC5C;AACA;AAAA,IACF;AACA,gBAAY,KAAK,aAAaA,IAAG,GAAG,CAAC,CAAC;AAAA,EACxC;AACA,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC;AAAA,EACV;AACA,QAAM,WAAW,CAAC;AAClB,SAAO,SAAS,yBAAyB,UAAU,aAAa,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC,GAAG,MAAM;AAC/F,MAAE,KAAK,EAAE;AACT,MAAE,SAAS,EAAE,KAAK,EAAE;AACpB,MAAE,MAAM,EAAE,MAAM,iBAAiB;AACjC,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AA+GA,IAAM,gBAAgC,oBAAI,IAAI,CAAC,UAAU,WAAW,WAAW,cAAc,aAAa,CAAC;AAG3G,IAAM,cAAc;AAAA;AAAA,EAElB,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,cAAc;AAAA;AAAA,EAElB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AACP;AACA,SAAS,UAAU,KAAK;AACtB,QAAM,WAAW,IAAI;AACrB,MAAI,OAAO,aAAa;AACtB,WAAO;AACT,MAAI,SAAS;AACb,MAAI,IAAI,QAAQ,QAAQ;AACtB,QAAI,IAAI,MAAM,YAAY,MAAM;AAC9B,eAAS;AAAA,aACF,IAAI,MAAM;AACjB,eAAS;AAAA,aACF,IAAI,MAAM,SAAS;AAC1B,eAAS;AAAA,EACb,WAAW,IAAI,QAAQ,UAAU,IAAI,MAAM,QAAQ,cAAc;AAC/D,aAAS;AAAA,EACX,WAAW,IAAI,OAAO,aAAa;AACjC,aAAS,YAAY,IAAI,GAAG;AAAA,EAC9B;AACA,MAAI,YAAY,YAAY,aAAa;AACvC,WAAO,SAAS,YAAY,QAAQ;AAAA,EACtC;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,CAAC,EAAE,QAAQ,WAAW,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,QAAQ,EAAE,CAAC;AAEzF,IAAM,wBAAwB,CAAC,QAAQ,YAAY,YAAY;AAC/D,SAAS,aAAa,KAAK;AACzB,QAAM,EAAE,OAAO,KAAK,QAAQ,IAAI;AAChC,MAAI,WAAW,IAAI,OAAO;AACxB,WAAO;AACT,MAAI,YAAY,UAAU,MAAM,QAAQ;AACtC,WAAO;AACT,MAAI,MAAM;AACR,WAAO;AACT,MAAI,MAAM,IAAI;AACZ,WAAO,GAAG,OAAO,OAAO,MAAM,EAAE;AAAA,EAClC;AACA,aAAW,KAAK,uBAAuB;AACrC,QAAI,MAAM,CAAC,MAAM,QAAQ;AACvB,aAAO,GAAG,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,SAAS;AACf,SAAS,IAAIC,IAAG,OAAO,SAAS,OAAO;AA1kBvC;AA2kBE,MAAI;AACJ,MAAI,UAAU,OAAO,UAAU,aAAa;AAC1C,UAAMA,GAAE;AAAA,EACV,WAAW,MAAM,SAAS,GAAG,GAAG;AAC9B,UAAM,WAAW,MAAM,QAAQ,GAAG;AAClC,WAAM,KAAAA,GAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAA9B,mBAAkC,MAAM,UAAU,WAAW,CAAC;AAAA,EACtE,OAAO;AACL,UAAMA,GAAE,KAAK;AAAA,EACf;AACA,MAAI,QAAQ,QAAQ;AAClB,WAAO,UAAU,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI,OAAO;AAAA,EAC5D;AACA,SAAO;AACT;AACA,IAAM,WAAW,IAAI,OAAO,GAAG,MAAM,UAAU,MAAM,MAAM,GAAG;AAC9D,SAAS,sBAAsB,GAAGA,IAAG,KAAK,SAAS,OAAO;AACxD,MAAI,OAAO,MAAM,YAAY,CAAC,EAAE,SAAS,GAAG;AAC1C,WAAO;AACT,MAAI,UAAU;AACd,MAAI;AACF,cAAU,UAAU,CAAC;AAAA,EACvB,QAAQ;AAAA,EACR;AACA,QAAM,SAAS,QAAQ,MAAM,iBAAiB;AAC9C,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,YAAY,EAAE,SAAS,MAAM;AACnC,MAAI,EAAE,QAAQ,mBAAmB,CAAC,UAAU;AAC1C,QAAI,UAAU,UAAU,CAAC,OAAO,SAAS,KAAK,GAAG;AAC/C,aAAO;AAAA,IACT;AACA,UAAM,KAAK,IAAIA,IAAG,MAAM,MAAM,CAAC,GAAG,MAAM;AACxC,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B,CAAC,EAAE,KAAK;AACR,MAAI,WAAW;AACb,QAAI,EAAE,SAAS,MAAM;AACnB,UAAI,EAAE,MAAM,GAAG,CAAC,OAAO,MAAM;AAC/B,QAAI,EAAE,WAAW,MAAM;AACrB,UAAI,EAAE,MAAM,OAAO,MAAM;AAC3B,QAAI,EAAE,QAAQ,UAAU,GAAG,EAAE,KAAK;AAAA,EACpC;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,UAAU,OAAO;AAC7C,MAAI,YAAY;AACd,WAAO,SAAS;AAClB,MAAI,OAAO,aAAa;AACtB,WAAO,SAAS,KAAK;AACvB,SAAO;AACT;;;AC5nBA,eAAe,cAAc,MAAM,UAAU,CAAC,GAAG;AAC/C,QAAM,MAAM,QAAQ,YAAY,KAAK,gBAAgB;AACrD,MAAI,CAAC,OAAO,CAAC,KAAK;AAChB;AACF,QAAM,kBAAkB,EAAE,cAAc,MAAM,MAAM,CAAC,EAAE;AACvD,QAAM,KAAK,MAAM,SAAS,oBAAoB,eAAe;AAC7D,MAAI,CAAC,gBAAgB;AACnB;AACF,MAAI,KAAK,mBAAmB;AAC1B,WAAO,KAAK;AAAA,EACd;AACA,OAAK,oBAAoB,IAAI,QAAQ,OAAO,YAAY;AAb1D;AAcI,UAAM,QAAQ,MAAM,KAAK,YAAY,GAAG,IAAI,CAAC,SAAS;AAAA,MACpD;AAAA,MACA,IAAI,eAAe,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAI,IAAI;AAAA,MACrD,cAAc;AAAA,IAChB,EAAE;AACF,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,OAAO;AACV,cAAQ;AAAA,QACN,OAAO,EAAE,WAAW,IAAI,iBAAiB,WAAW,IAAI,KAAK;AAAA,MAC/D;AACA,YAAM,kBAAkC,oBAAI,IAAI;AAChD,iBAAW,OAAO,CAAC,QAAQ,MAAM,GAAG;AAClC,cAAM,YAAW,SAAI,GAAG,MAAP,mBAAU;AAC3B,mBAAW,KAAK,UAAU;AACxB,gBAAM,MAAM,EAAE,QAAQ,YAAY;AAClC,cAAI,CAAC,eAAe,IAAI,GAAG,GAAG;AAC5B;AAAA,UACF;AACA,gBAAM,IAAI;AAAA,YACR;AAAA,YACA,OAAO,MAAM;AAAA,cACX,EAAE,kBAAkB,EAAE,OAAO,CAAC,OAAO,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,CAAC,CAAC;AAAA,YAChG;AAAA,YACA,WAAW,EAAE;AAAA,UACf;AACA,gBAAM,YAAY,aAAa,CAAC;AAChC,cAAI,IAAI;AACR,cAAI,IAAI;AACR,iBAAO,KAAK,gBAAgB,IAAI,CAAC;AAC/B,gBAAI,GAAG,SAAS,IAAI,GAAG;AACzB,cAAI,GAAG;AACL,cAAE,KAAK;AACP,4BAAgB,IAAI,CAAC;AAAA,UACvB;AACA,gBAAM,MAAM,EAAE,aAAa,UAAU,KAAK,QAAQ,CAAC,CAAC,IAAI;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AACA,UAAM,qBAAqB,EAAE,GAAG,MAAM,YAAY;AAClD,UAAM,cAAc,CAAC;AACrB,aAAS,MAAM,IAAI,OAAO,IAAI;AAC5B,YAAMC,KAAI,GAAG,EAAE,IAAI,KAAK;AACxB,YAAM,YAAYA,EAAC,IAAI;AACvB,aAAO,MAAM,mBAAmBA,EAAC;AAAA,IACnC;AACA,aAAS,SAAS,EAAE,IAAI,KAAK,IAAI,GAAG;AAClC,YAAM,YAAY,IAAI,IAAI,SAAS,OAAO;AAC1C,YAAM,MAAM,EAAE,IAAI;AAClB,UAAI,CAAC,WAAW;AACd,YAAI,IAAI,eAAe,IAAI,gBAAgB,IAAI,aAAa;AAC1D,cAAI,cAAc,IAAI;AAAA,QACxB;AACA,YAAI,IAAI,aAAa,IAAI,cAAc,IAAI,WAAW;AACpD,cAAI,YAAY,IAAI;AAAA,QACtB;AACA,cAAM,IAAI,MAAM,MAAM;AArE9B,cAAAC;AAsEU,WAAAA,MAAA,MAAM,MAAM,EAAE,MAAd,gBAAAA,IAAiB;AACjB,iBAAO,MAAM,MAAM,EAAE;AAAA,QACvB,CAAC;AAAA,MACH;AACA,UAAI,IAAI,gBAAgB;AACtB,mBAAWD,MAAK,IAAI,gBAAgB;AAClC,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,IAAI,gBAAgBA,EAAC,GAAG;AAChE;AAAA,UACF;AACA,cAAI,IAAI,aAAa,QAAQA,EAAC,EAAE,MAAM,IAAI;AACxC,aAAC,IAAI,QAAQ,cAAc,IAAI,cAAc,KAAK;AAAA;AAAA,cAEhDA,GAAE,UAAU,CAAC;AAAA,cACb,IAAI,eAAeA,EAAC,EAAE,KAAK,GAAG;AAAA,YAChC;AACA,gBAAI,aAAa,QAAQA,EAAC,IAAI,EAAE;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,iBAAWA,MAAK,IAAI,OAAO;AACzB,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,IAAI,OAAOA,EAAC,GAAG;AACvD;AAAA,QACF;AACA,cAAM,QAAQ,IAAI,MAAMA,EAAC;AACzB,cAAM,KAAK,QAAQA,EAAC;AACpB,YAAIA,OAAM,SAAS;AACjB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,qBAAW,KAAK,MAAM,MAAM,GAAG,GAAG;AAChC,yBAAa,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,MAAM,IAAI,UAAU,OAAO,CAAC,CAAC;AAClE,aAAC,IAAI,UAAU,SAAS,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC;AAAA,UACnD;AAAA,QACF,WAAWA,OAAM,SAAS;AACxB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,qBAAW,KAAK,MAAM,MAAM,GAAG,GAAG;AAChC,kBAAM,YAAY,EAAE,QAAQ,GAAG;AAC/B,kBAAME,MAAK,EAAE,UAAU,GAAG,SAAS,EAAE,KAAK;AAC1C,kBAAM,IAAI,EAAE,UAAU,YAAY,CAAC,EAAE,KAAK;AAC1C,kBAAM,IAAI,GAAG,EAAE,IAAIA,GAAE,IAAI,MAAM;AAC7B,kBAAI,MAAM,eAAeA,GAAE;AAAA,YAC7B,CAAC;AACD,gBAAI,MAAM,YAAYA,KAAI,CAAC;AAAA,UAC7B;AAAA,QACF,OAAO;AACL,cAAI,aAAaF,EAAC,MAAM,SAAS,IAAI,aAAaA,IAAG,UAAU,OAAO,KAAK,OAAO,KAAK,CAAC;AACxF,uBAAa,MAAM,IAAI,IAAI,MAAM,IAAI,gBAAgBA,EAAC,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,IACR;AACA,eAAW,OAAO,MAAM;AACtB,YAAM,EAAE,KAAK,cAAc,GAAG,IAAI;AAClC,UAAI,CAAC;AACH;AACF,UAAI,IAAI,QAAQ,SAAS;AACvB,YAAI,QAAQ,IAAI;AAChB;AAAA,MACF;AACA,UAAI,MAAM,IAAI,OAAO,MAAM,MAAM,EAAE;AACnC,UAAI,IAAI,KAAK;AACX,iBAAS,GAAG;AAAA,MACd,WAAW,eAAe,IAAI,IAAI,GAAG,GAAG;AACtC,gBAAQ,KAAK,GAAG;AAAA,MAClB;AAAA,IACF;AACA,eAAW,OAAO,SAAS;AACzB,YAAM,MAAM,IAAI,IAAI,eAAe;AACnC,UAAI,MAAM,IAAI,cAAc,IAAI,IAAI,GAAG;AACvC,eAAS,GAAG;AACZ,WAAK,GAAG,IAAI,KAAK,GAAG,KAAK,IAAI,uBAAuB;AACpD,WAAK,GAAG,EAAE,YAAY,IAAI,GAAG;AAAA,IAC/B;AACA,eAAW,OAAO;AAChB,YAAM,KAAK,MAAM,SAAS,iBAAiB,KAAK,KAAK,KAAK;AAC5D,SAAK,QAAQ,IAAI,KAAK,YAAY,KAAK,IAAI;AAC3C,SAAK,YAAY,IAAI,KAAK,aAAa,KAAK,UAAU,IAAI,KAAK,UAAU;AACzE,SAAK,aAAa,IAAI,KAAK,YAAY,KAAK,SAAS;AACrD,eAAWA,MAAK,MAAM,oBAAoB;AACxC,YAAM,mBAAmBA,EAAC,EAAE;AAAA,IAC9B;AACA,SAAK,OAAO;AACZ,UAAM,KAAK,MAAM,SAAS,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAC3D,YAAQ;AAAA,EACV,CAAC,EAAE,QAAQ,MAAM;AACf,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf,CAAC;AACD,SAAO,KAAK;AACd;AAEA,SAAS,uBAAuB,MAAM,UAAU,CAAC,GAAG;AAClD,QAAM,KAAK,QAAQ,YAAY,CAAC,QAAQ,WAAW,KAAK,EAAE;AAC1D,SAAO,KAAK,6BAA6B,KAAK,8BAA8B,IAAI,QAAQ,CAAC,YAAY,GAAG,MAAM;AAC5G,WAAO,cAAc,MAAM,OAAO,EAAE,KAAK,MAAM;AAC7C,aAAO,KAAK;AACZ,cAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AAGA,SAAS,UAAU,SAAS;AAC1B,SAAO,iBAAiB,CAAC,SAAS;AApLpC;AAqLI,UAAM,mBAAiB,gBAAK,gBAAgB,aAArB,mBAA+B,KAAK,cAAc,mCAAlD,mBAAkF,cAAa;AACtH,QAAI,gBAAgB;AAClB,WAAK,KAAK,KAAK,MAAM,cAAc,CAAC;AAAA,IACtC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,mBAAmB,CAAC,UAAU;AAC5B,iCAAuB,OAAO,OAAO;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AClMA,SAAS,UAAU,aAAa,QAAQ,CAAC,GAAG,YAAY;AACtD,aAAW,OAAO,aAAa;AAC7B,UAAM,UAAU,YAAY,GAAG;AAC/B,UAAM,OAAO,aAAa,GAAG,UAAU,IAAI,GAAG,KAAK;AACnD,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,gBAAU,SAAS,OAAO,IAAI;AAAA,IAChC,WAAW,OAAO,YAAY,YAAY;AACxC,YAAM,IAAI,IAAI;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AA6BA,IAAM,cAAc,EAAE,KAAK,CAAC,cAAc,UAAU,EAAE;AACtD,IAAM,cAAc,MAAM;AAC1B,IAAM,aAAa,OAAO,QAAQ,eAAe,cAAc,QAAQ,aAAa;AACpF,SAAS,iBAAiB,OAAO,MAAM;AACrC,QAAM,OAAO,KAAK,MAAM;AACxB,QAAM,OAAO,WAAW,IAAI;AAC5B,SAAO,MAAM;AAAA,IACX,CAAC,SAAS,iBAAiB,QAAQ,KAAK,MAAM,KAAK,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC;AAAA,IACnF,QAAQ,QAAQ;AAAA,EAClB;AACF;AACA,SAAS,mBAAmB,OAAO,MAAM;AACvC,QAAM,OAAO,KAAK,MAAM;AACxB,QAAM,OAAO,WAAW,IAAI;AAC5B,SAAO,QAAQ,IAAI,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AACvE;AAUA,SAAS,aAAa,WAAW,MAAM;AACrC,aAAW,YAAY,CAAC,GAAG,SAAS,GAAG;AACrC,aAAS,IAAI;AAAA,EACf;AACF;AAEA,IAAM,WAAN,MAAe;AAAA,EACb,cAAc;AACZ,SAAK,SAAS,CAAC;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,CAAC;AACzB,SAAK,OAAO,KAAK,KAAK,KAAK,IAAI;AAC/B,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAAA,EACjD;AAAA,EACA,KAAK,MAAM,WAAW,UAAU,CAAC,GAAG;AAClC,QAAI,CAAC,QAAQ,OAAO,cAAc,YAAY;AAC5C,aAAO,MAAM;AAAA,MACb;AAAA,IACF;AACA,UAAM,eAAe;AACrB,QAAI;AACJ,WAAO,KAAK,iBAAiB,IAAI,GAAG;AAClC,YAAM,KAAK,iBAAiB,IAAI;AAChC,aAAO,IAAI;AAAA,IACb;AACA,QAAI,OAAO,CAAC,QAAQ,iBAAiB;AACnC,UAAI,UAAU,IAAI;AAClB,UAAI,CAAC,SAAS;AACZ,kBAAU,GAAG,YAAY,+BAA+B,IAAI,KAAK,gBAAgB,IAAI,EAAE,KAAK;AAAA,MAC9F;AACA,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,sBAAsC,oBAAI,IAAI;AAAA,MACrD;AACA,UAAI,CAAC,KAAK,oBAAoB,IAAI,OAAO,GAAG;AAC1C,gBAAQ,KAAK,OAAO;AACpB,aAAK,oBAAoB,IAAI,OAAO;AAAA,MACtC;AAAA,IACF;AACA,QAAI,CAAC,UAAU,MAAM;AACnB,UAAI;AACF,eAAO,eAAe,WAAW,QAAQ;AAAA,UACvC,KAAK,MAAM,MAAM,KAAK,QAAQ,QAAQ,GAAG,IAAI;AAAA,UAC7C,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,QAAQ;AAAA,MACR;AAAA,IACF;AACA,SAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAC1C,SAAK,OAAO,IAAI,EAAE,KAAK,SAAS;AAChC,WAAO,MAAM;AACX,UAAI,WAAW;AACb,aAAK,WAAW,MAAM,SAAS;AAC/B,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,MAAM,WAAW;AACxB,QAAI;AACJ,QAAI,YAAY,IAAI,eAAe;AACjC,UAAI,OAAO,WAAW,YAAY;AAChC,eAAO;AAAA,MACT;AACA,eAAS;AACT,kBAAY;AACZ,aAAO,UAAU,GAAG,UAAU;AAAA,IAChC;AACA,aAAS,KAAK,KAAK,MAAM,SAAS;AAClC,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM,WAAW;AAC1B,QAAI,KAAK,OAAO,IAAI,GAAG;AACrB,YAAM,QAAQ,KAAK,OAAO,IAAI,EAAE,QAAQ,SAAS;AACjD,UAAI,UAAU,IAAI;AAChB,aAAK,OAAO,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,OAAO,IAAI,EAAE,WAAW,GAAG;AAClC,eAAO,KAAK,OAAO,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,MAAM,YAAY;AAC9B,SAAK,iBAAiB,IAAI,IAAI,OAAO,eAAe,WAAW,EAAE,IAAI,WAAW,IAAI;AACpF,UAAM,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC;AACrC,WAAO,KAAK,OAAO,IAAI;AACvB,eAAW,QAAQ,QAAQ;AACzB,WAAK,KAAK,MAAM,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EACA,eAAe,iBAAiB;AAC9B,WAAO,OAAO,KAAK,kBAAkB,eAAe;AACpD,eAAW,QAAQ,iBAAiB;AAClC,WAAK,cAAc,MAAM,gBAAgB,IAAI,CAAC;AAAA,IAChD;AAAA,EACF;AAAA,EACA,SAAS,aAAa;AACpB,UAAM,QAAQ,UAAU,WAAW;AACnC,UAAM,YAAY,OAAO,KAAK,KAAK,EAAE;AAAA,MACnC,CAAC,QAAQ,KAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAAA,IACpC;AACA,WAAO,MAAM;AACX,iBAAW,SAAS,UAAU,OAAO,GAAG,UAAU,MAAM,GAAG;AACzD,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,aAAa;AACvB,UAAM,QAAQ,UAAU,WAAW;AACnC,eAAW,OAAO,OAAO;AACvB,WAAK,WAAW,KAAK,MAAM,GAAG,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,eAAW,OAAO,KAAK,QAAQ;AAC7B,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS,SAAS,YAAY;AAC5B,eAAW,QAAQ,IAAI;AACvB,WAAO,KAAK,aAAa,kBAAkB,MAAM,GAAG,UAAU;AAAA,EAChE;AAAA,EACA,iBAAiB,SAAS,YAAY;AACpC,eAAW,QAAQ,IAAI;AACvB,WAAO,KAAK,aAAa,oBAAoB,MAAM,GAAG,UAAU;AAAA,EAClE;AAAA,EACA,aAAa,QAAQ,SAAS,YAAY;AACxC,UAAM,QAAQ,KAAK,WAAW,KAAK,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,CAAC,EAAE,IAAI;AACtF,QAAI,KAAK,SAAS;AAChB,mBAAa,KAAK,SAAS,KAAK;AAAA,IAClC;AACA,UAAM,SAAS;AAAA,MACb,QAAQ,KAAK,SAAS,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC;AAAA,MAChD;AAAA,IACF;AACA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,QAAQ,MAAM;AAC1B,YAAI,KAAK,UAAU,OAAO;AACxB,uBAAa,KAAK,QAAQ,KAAK;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU,OAAO;AACxB,mBAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,UAAU,KAAK,WAAW,CAAC;AAChC,SAAK,QAAQ,KAAK,SAAS;AAC3B,WAAO,MAAM;AACX,UAAI,KAAK,YAAY,QAAQ;AAC3B,cAAM,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AAC5C,YAAI,UAAU,IAAI;AAChB,eAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,WAAW;AACnB,SAAK,SAAS,KAAK,UAAU,CAAC;AAC9B,SAAK,OAAO,KAAK,SAAS;AAC1B,WAAO,MAAM;AACX,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM,QAAQ,KAAK,OAAO,QAAQ,SAAS;AAC3C,YAAI,UAAU,IAAI;AAChB,eAAK,OAAO,OAAO,OAAO,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc;AACrB,SAAO,IAAI,SAAS;AACtB;;;AC3OA,IAAM,oBAAoC,oBAAI,IAAI,CAAC,kBAAkB,aAAa,WAAW,CAAC;AAC9F,IAAM,eAAe,iBAAiB;AAAA,EACpC,OAAO;AAAA,IACL,iBAAiB,CAAC,EAAE,IAAI,MAAM;AAC5B,UAAI,IAAI,MAAM,KAAK;AACjB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,UAAI,IAAI,MAAM,MAAM;AAClB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,UAAI,IAAI,MAAM,KAAK;AACjB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,YAAM,eAAe,aAAa,GAAG;AACrC,UAAI,gBAAgB,CAAC,aAAa,WAAW,UAAU,KAAK,CAAC,aAAa,WAAW,eAAe,GAAG;AACrG,eAAO,IAAI;AAAA,MACb;AACA,YAAM,SAAS,iBAAiB,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AACpE,UAAI;AACF,YAAI,KAAK;AAAA,IACb;AAAA,IACA,gBAAgB,CAAC,QAAQ;AACvB,YAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,iBAAW,OAAO,IAAI,MAAM;AAC1B,cAAM,aAAa,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,QAAQ,GAAG;AAC7E,cAAM,WAAW,SAAS,SAAS;AACnC,YAAI,UAAU;AACZ,cAAI,WAAW,2BAAK;AACpB,cAAI,CAAC,YAAY,kBAAkB,IAAI,IAAI,GAAG;AAC5C,uBAAW;AACb,cAAI,aAAa,SAAS;AACxB,kBAAM,WAAW,SAAS;AAC1B,gBAAI,SAAS,SAAS,IAAI,MAAM,OAAO;AACrC,kBAAI,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC,MAAM,KAAK;AACrD,yBAAS,SAAS;AAAA,cACpB;AACA,kBAAI,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA,YACxD;AACA,gBAAI,SAAS,SAAS,IAAI,MAAM,OAAO;AACrC,kBAAI,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA,YACxD,WAAW,SAAS,OAAO;AACzB,kBAAI,MAAM,QAAQ,SAAS;AAAA,YAC7B;AACA,qBAAS,SAAS,EAAE,QAAQ;AAAA,cAC1B,GAAG;AAAA,cACH,GAAG,IAAI;AAAA,YACT;AACA;AAAA,UACF,WAAW,IAAI,OAAO,SAAS,IAAI;AACjC,qBAAS,SAAS,SAAS,UAAU,CAAC;AACtC,gBAAI,KAAK,GAAG,SAAS,EAAE,IAAI,SAAS,OAAO,SAAS,CAAC;AACrD,qBAAS,OAAO,KAAK,GAAG;AACxB;AAAA,UACF,WAAW,UAAU,GAAG,IAAI,UAAU,QAAQ,GAAG;AAC/C;AAAA,UACF;AAAA,QACF;AACA,cAAM,WAAW,IAAI,aAAa,IAAI,eAAe,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW;AACvF,YAAI,CAAC,YAAY,eAAe,IAAI,IAAI,GAAG,GAAG;AAC5C,iBAAO,SAAS,SAAS;AACzB;AAAA,QACF;AACA,iBAAS,SAAS,IAAI;AAAA,MACxB;AACA,YAAM,UAAU,CAAC;AACjB,iBAAW,OAAO,UAAU;AAC1B,cAAM,MAAM,SAAS,GAAG;AACxB,cAAM,QAAQ,IAAI;AAClB,gBAAQ,KAAK,GAAG;AAChB,YAAI,OAAO;AACT,iBAAO,IAAI;AACX,kBAAQ,KAAK,GAAG,KAAK;AAAA,QACvB;AAAA,MACF;AACA,UAAI,OAAO;AACX,UAAI,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,WAAW,EAAE,MAAM,QAAQ,EAAE,MAAM,aAAa,CAAC,EAAE,MAAM,QAAQ;AAAA,IACjH;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiC,oBAAI,IAAI,CAAC,UAAU,QAAQ,WAAW,CAAC;AAC9E,IAAM,sBAAsB,iBAAiB,CAAC,UAAU;AAAA,EACtD,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AACvB,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,CAAC,eAAe,IAAI,IAAI,GAAG,GAAG;AAChC;AAAA,QACF;AACA,cAAM,QAAQ,IAAI;AAClB,mBAAW,OAAO,OAAO;AACvB,cAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;AACpC;AAAA,UACF;AACA,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACrD;AAAA,UACF;AACA,gBAAM,QAAQ,MAAM,GAAG;AACvB,cAAI,OAAO,UAAU,YAAY;AAC/B;AAAA,UACF;AACA,cAAI,KAAK,OAAO,cAAc,IAAI,GAAG,GAAG;AACtC,kBAAM,GAAG,IAAI,gBAAgB,GAAG;AAAA,UAClC,OAAO;AACL,mBAAO,MAAM,GAAG;AAAA,UAClB;AACA,cAAI,iBAAiB,IAAI,kBAAkB,CAAC;AAC5C,cAAI,eAAe,GAAG,IAAI;AAAA,QAC5B;AACA,YAAI,KAAK,OAAO,IAAI,mBAAmB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO;AACvE,cAAI,MAAM,IAAI,OAAO,SAAS,IAAI,MAAM,OAAO,IAAI,MAAM,IAAI;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,EAAE,KAAK,IAAI,MAAM;AAzHvC;AA0HM,YAAM,UAAU,2BAAK;AACrB,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,iBAAWG,MAAK,SAAS;AACvB,YAAI,CAACA,GAAE,SAAS,OAAO,GAAG;AACxB;AAAA,QACF;AACA,cAAM,KAAKA,GAAE,MAAM,GAAG,EAAE;AACxB,YAAI,CAAC,cAAc,IAAI,EAAE,GAAG;AAC1B;AAAA,QACF;AACA,wBAAI,mBAAJ,mBAAqB,QAArB,mBAA0B,KAAK,KAAK,IAAI,MAAM,GAAG,UAAU,CAAC,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAM,eAA+B,oBAAI,IAAI,CAAC,QAAQ,SAAS,UAAU,UAAU,CAAC;AACpF,IAAM,kBAAkB,iBAAiB;AAAA,EACvC,OAAO;AAAA,IACL,iBAAiB,CAAC,EAAE,IAAI,MAAM;AAC5B,UAAI,IAAI,OAAO,aAAa,IAAI,IAAI,GAAG,GAAG;AACxC,YAAI,MAAM,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI,GAAG;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,gBAAgB,iBAAiB;AAAA,EACrC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,sBAAsB,CAAC,QAAQ;AAC7B,YAAM,UAAU,CAAC;AACjB,UAAI,aAAa;AACjB,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,IAAI,OAAO,YAAY,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,oBAAoB,IAAI,QAAQ,SAAS;AAC7G;AAAA,QACF;AACA,gBAAQ,IAAI,GAAG,IAAI,IAAI,QAAQ,WAAW,IAAI,QAAQ,kBAAkB,IAAI,cAAc,IAAI;AAC9F,qBAAa;AAAA,MACf;AACA,UAAI,YAAY;AACd,YAAI,KAAK,KAAK;AAAA,UACZ,KAAK;AAAA,UACL,WAAW,KAAK,UAAU,OAAO;AAAA,UACjC,OAAO,EAAE,IAAI,kBAAkB,MAAM,mBAAmB;AAAA,QAC1D,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa,iBAAiB;AAAA,EAClC,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AAjL7B;AAkLM,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,OAAO,IAAI,gBAAgB,UAAU;AACvC;AAAA,QACF;AACA,mBAAW,EAAE,QAAQ,OAAO,KAAK,eAAe;AAC9C,cAAI,CAAC,IAAI,YAAY,WAAW,MAAM,GAAG;AACvC;AAAA,UACF;AACA,gBAAM,MAAM,IAAI,YAAY,UAAU,OAAO,MAAM;AACnD,gBAAM,YAAW,SAAI,KAAK,KAAK,CAAC,SAAS,KAAK,OAAO,GAAG,MAAvC,mBAA0C;AAC3D,cAAI,aAAa,QAAQ;AACvB,gBAAI,KAAK,WAAW;AACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,KAAK,CAAC,GAAG,MAAM;AACtB,cAAM,UAAU,UAAU,CAAC;AAC3B,cAAM,UAAU,UAAU,CAAC;AAC3B,YAAI,UAAU,SAAS;AACrB,iBAAO;AAAA,QACT,WAAW,UAAU,SAAS;AAC5B,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,KAAK,EAAE;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AACb;AACA,IAAM,eAAe,CAAC,aAAa,aAAa;AAChD,IAAM,uBAAuB,iBAAiB,CAAC,UAAU;AAAA,EACvD,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AAxN7B;AAyNM,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,kBAAkB;AAChC;AAAA,QACF;AACA,yBAAiB,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,CAAC,EAAE;AAC1C,aAAK;AAAA,MACP;AACA,YAAM,SAAS,kBAAkB,CAAC;AAClC,YAAM,MAAM,OAAO,aAAa;AAChC,aAAO,OAAO;AACd,aAAO,YAAY;AAAA;AAAA,QAEjB,OAAO,eAAa,UAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,OAAO,MAAtC,mBAAyC,gBAAe;AAAA,QAC5E;AAAA,QACA;AAAA,MACF;AACA,iBAAW,OAAO,MAAM;AACtB,YAAI,IAAI,0BAA0B,OAAO;AACvC;AAAA,QACF;AACA,cAAM,IAAI,eAAe,IAAI,GAAG;AAChC,YAAI,KAAK,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU;AACzC,cAAI,MAAM,CAAC,IAAI,sBAAsB,IAAI,MAAM,CAAC,GAAG,QAAQ,GAAG;AAAA,QAChE,WAAW,IAAI,yBAAyB,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,SAAS;AAC1F,qBAAWC,MAAK,cAAc;AAC5B,gBAAI,OAAO,IAAIA,EAAC,MAAM;AACpB,kBAAIA,EAAC,IAAI,sBAAsB,IAAIA,EAAC,GAAG,QAAQ,KAAK,IAAI,QAAQ,YAAY,IAAI,MAAM,KAAK,SAAS,MAAM,CAAC;AAAA,UAC/G;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAAA,IACpB;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAAM;AACjC,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,WAAW,IAAI,0BAA0B,OAAO;AAC9D,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,+BAAO,aAAa;AACtB,cAAM,cAAc,sBAAsB,MAAM,aAAa,KAAK,iBAAiB,KAAK,UAAU;AAAA,MACpG;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAM,sBAAsB,iBAAiB;AAAA,EAC3C,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AACvB,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI;AACJ,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,SAAS;AACvB,qBAAW;AAAA,QACb,WAAW,IAAI,QAAQ,iBAAiB;AACtC,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,UAAI,oBAAoB,UAAU;AAChC,cAAM,WAAW;AAAA,UACf,iBAAiB;AAAA,UACjB,SAAS;AAAA,QACX;AACA,YAAI,aAAa,MAAM;AACrB,mBAAS,cAAc,YAAY,SAAS;AAAA,QAC9C,OAAO;AACL,cAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,QAC/C;AAAA,MACF,WAAW,kBAAkB;AAC3B,cAAM,WAAW;AAAA,UACf,iBAAiB;AAAA,QACnB;AACA,YAAI,aAAa,MAAM;AACrB,2BAAiB,cAAc;AAC/B,2BAAiB,MAAM;AACvB,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,YAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,gBAAgB,GAAG,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,YAAY,iBAAiB;AAAA,EACjC,OAAO;AAAA,IACL,qBAAqB,CAAC,QAAQ;AAC5B,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,OAAO,IAAI,cAAc,UAAU;AACrC,cAAI,IAAI,cAAc,IAAI,MAAM,SAAS,yBAAyB,IAAI,MAAM,SAAS,qBAAqB;AACxG,gBAAI,YAAY,IAAI,UAAU,QAAQ,MAAM,SAAS;AAAA,UACvD,OAAO;AACL,gBAAI,YAAY,IAAI,UAAU,QAAQ,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,EAAE;AAAA,UACzF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAI;AAEJ,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM,OAAO,eAAe,OAAO;AACnC,OAAK,IAAI,UAAU,CAAC;AACpB,SAAO,aAAa;AACtB;AAKA,SAAS,WAAW,MAAM,KAAK;AAC7B,SAAO,CAAC,QAAQ,SAAS,YAAY,OAAO,SAAS,YAAY,CAAC;AACpE;AACA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM,QAAQ,YAAY;AAC1B,QAAM,SAAS,QAAQ,SAAS,CAAC,CAAC;AAClC,UAAQ,WAAW,QAAQ,aAAa,YAAY,WAAW;AAC/D,QAAM,MAAM,CAAC,QAAQ;AACrB,QAAM,UAAU,MAAM;AACpB,SAAK,QAAQ;AACb,UAAM,SAAS,mBAAmB,IAAI;AAAA,EACxC;AACA,MAAI,aAAa;AACjB,MAAI,UAAU,CAAC;AACf,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB;AAAA,IACA,cAAc;AACZ,aAAO;AAAA,IACT;AAAA,IACA,IAAIC,IAAG;AACL,YAAM,SAAS,OAAOA,OAAM,aAAaA,GAAE,IAAI,IAAIA;AACnD,UAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,KAAK,CAACC,QAAOA,IAAG,QAAQ,OAAO,GAAG,GAAG;AAC/D,gBAAQ,KAAK,MAAM;AACnB,mBAAW,OAAO,MAAM,GAAG,KAAK,MAAM,SAAS,OAAO,SAAS,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,IACA,KAAK,OAAO,cAAc;AACxB,wDAAqB;AACrB,YAAM,QAAQ;AAAA,QACZ,IAAI;AAAA,QACJ;AAAA,QACA,GAAG;AAAA,MACL;AACA,UAAI,WAAW,MAAM,MAAM,GAAG,GAAG;AAC/B,gBAAQ,KAAK,KAAK;AAClB,gBAAQ;AAAA,MACV;AACA,aAAO;AAAA,QACL,UAAU;AACR,oBAAU,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AACjD,kBAAQ;AAAA,QACV;AAAA;AAAA,QAEA,MAAM,QAAQ;AACZ,qBAAW,KAAK,SAAS;AACvB,gBAAI,EAAE,OAAO,MAAM,IAAI;AACrB,gBAAE,QAAQ,MAAM,QAAQ;AAAA,YAC1B;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,cAAc;AAClB,YAAM,aAAa,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,OAAO,EAAE;AACrD,YAAM,MAAM,SAAS,mBAAmB,UAAU;AAClD,iBAAW,SAAS,WAAW,SAAS;AACtC,cAAM,WAAW,MAAM,iBAAiB,MAAM;AAC9C,cAAM,gBAAgB,OAAO,MAAM,YAAY,MAAM,UAAU,QAAQ,IAAI;AAC3E,YAAI,MAAM,eAAe;AACvB,qBAAW,OAAO,MAAM,mBAAmB,KAAK,GAAG;AACjD,kBAAM,SAAS,EAAE,KAAK,OAAO,iBAAiB,KAAK,gBAAgB;AACnE,kBAAM,MAAM,SAAS,iBAAiB,MAAM;AAC5C,uBAAW,KAAK,KAAK,OAAO,GAAG;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AACA,YAAM,MAAM,SAAS,sBAAsB,UAAU;AACrD,YAAM,MAAM,SAAS,gBAAgB,UAAU;AAC/C,YAAM,MAAM,SAAS,qBAAqB,UAAU;AACpD,aAAO,WAAW;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAG,mCAAS,YAAW,CAAC;AAAA,EAC1B,EAAE,QAAQ,CAACD,OAAM,KAAK,IAAIA,EAAC,CAAC;AAC5B,OAAK,MAAM,SAAS,QAAQ,IAAI;AAChC,SAAO;AACT;AAyBA,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,SAAS,cAAc;AACvB;AACA,YAAY,iBAAiB,IAAI;;;ACncjC,IAAM,OAAO,QAAQ,CAAC,MAAM;AAE5B,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AACA,SAAS,sBAAsBE,MAAK;AAClC,MAAIA,gBAAe,WAAWA,gBAAe,QAAQA,gBAAe;AAClE,WAAOA;AACT,QAAM,OAAO,aAAaA,IAAG;AAC7B,MAAI,CAACA,QAAO,CAAC;AACX,WAAO;AACT,MAAI,MAAM,QAAQ,IAAI;AACpB,WAAO,KAAK,IAAI,CAAC,MAAM,sBAAsB,CAAC,CAAC;AACjD,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,WAAW,CAAC;AAClB,eAAWC,MAAK,MAAM;AACpB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAMA,EAAC,GAAG;AAClD;AAAA,MACF;AACA,UAAIA,OAAM,mBAAmBA,GAAE,CAAC,MAAM,OAAOA,GAAE,CAAC,MAAM,KAAK;AACzD,iBAASA,EAAC,IAAI,MAAM,KAAKA,EAAC,CAAC;AAC3B;AAAA,MACF;AACA,eAASA,EAAC,IAAI,sBAAsB,KAAKA,EAAC,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,sBAAsB,iBAAiB;AAAA,EAC3C,OAAO;AAAA,IACL,mBAAmB,CAAC,QAAQ;AAC1B,iBAAW,SAAS,IAAI;AACtB,cAAM,gBAAgB,sBAAsB,MAAM,KAAK;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa;AACnB,SAAS,WAAW,MAAM;AACxB,QAAM,SAAS;AAAA,IACb,QAAQ,KAAK;AACX,UAAI,MAAM;AACR,YAAI,OAAO,iBAAiB,UAAU;AACtC,YAAI,OAAO,iBAAiB,QAAQ;AACpC,YAAI,QAAQ,YAAY,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AAOA,SAASC,YAAW,UAAU,CAAC,GAAG;AAChC,UAAQ,aAAa,QAAQ,eAAe,CAAC,OAAO,SAAS,MAAM,WAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAC5F,QAAM,OAAO,WAAa,OAAO;AACjC,OAAK,IAAI,mBAAmB;AAC5B,OAAK,UAAU,WAAW,IAAI;AAC9B,SAAO;AACT;;;AC5DA,IAAM,sBAAsB;AAAA,EAC1B;AACF;AACA,IAAM,8BAA8B;AAAA,EAClC,eAAe,CAAC,GAAG,qBAAqB,GAAG,eAAe;AAC5D;;;ACXA,SAAS,cAAc,cAAc;AACnC,MAAI,SAAS,eAAe,WAAW;AACrC,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAS,iBAAiB,oBAAoB,MAAM,QAAQ,YAAY,CAAC;AAAA,IAC3E,CAAC;AAAA,EACH;AACA,SAAO,QAAQ,QAAQ,YAAY;AACrC;AAEA,IAAM,aAAa,gBAAgB;AAAA,EACjC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,UAAU,IAAI,KAAK;AACzB,cAAU,MAAM,QAAQ,QAAQ,IAAI;AACpC,WAAO,MAAM;AACX,UAAI,CAAC,QAAQ;AACX,eAAO,MAAM,eAAe,MAAM,YAAY,CAAC,CAAC;AAClD,aAAO,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACF,CAAC;;;ACGD,SAAS,iBAAiB,OAAO;AAC/B,MAAI;AACF,WAAO,KAAK,MAAM,SAAS,IAAI;AAAA,EACjC,SAAS,OAAO;AACd,YAAQ,MAAM,oCAAoC,OAAO,KAAK;AAC9D,WAAO,CAAC;AAAA,EACV;AACF;;;ACzBA,SAAS,QAAQ,KAAK,eAAe,IAAI,UAAU,CAAC,GAAG;AACrD,QAAM;AAAA,IACJ;AAAA,IACA,qBAAqB;AAAA,IACrB,SAAAC,WAAU;AAAA,IACV,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,WAAW,OAAO,WAAW;AACnC,iBAAe,YAAY,SAAS,OAAO,WAAW;AACpD,UAAM,MAAM,SAAS,UAAU,GAAG,IAAI,aAAa,GAAG;AACtD,QAAI;AACJ,QAAIA,UAAS;AACX,aAAOC,YAAW;AAClB,UAAI,IAAI,IAAI;AAAA,IACd;AACA,UAAM,SAAS,aAAa;AAAA,MAC1B,SAAS,SAAS,iBAAiB,cAAc,IAAI,IAAI,oBAAoB,cAAc,IAAI;AAAA,MAC/F,GAAG;AAAA,IACL,CAAC;AACD,UAAM,EAAE,OAAO,IAAI;AACnB,QAAI;AACF,UAAI,UAAU,cAAc,UAAU;AACxC,UAAM,qBAAqB,CAAC;AAC5B,UAAM,mBAAmB,SAAS,MAAM;AAAA,IACxC,IAAI,CAAC,OAAO,mBAAmB,KAAK,EAAE;AACtC,UAAM,0BAA0B,MAAM;AACpC,aAAO,QAAQ,IAAI,mBAAmB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,IACzD;AACA,UAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,CAAC;AAAA,MACf;AAAA,MACA;AAAA,IACF;AACA,QAAI,QAAQ;AACV,YAAM,cAAc;AACpB,cAAQ,gBAAe,iDAAiB,OAAO,qBAAqB,CAAC,OAAM,iBAAiB,OAAO,iBAAiB;AAAA,IACtH;AACA,WAAM,yBAAK;AACX,QAAI,IAAI,MAAM;AACd,QAAI;AACJ,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,MAAM,SAAS;AACpC,UAAI,gBAAgB,kBAAkB,mBAAmB,GAAG,MAAM;AAChE,uBAAe;AACf,yBAAiB,GAAG;AACpB,WAAG,KAAK,QAAQ,QAAQ;AAAA,MAC1B;AACA,WAAK;AAAA,IACP,CAAC;AACD,QAAI,CAAC,QAAQ;AACX,YAAM,QAAQ,QAAQ,aAAa;AACnC,aAAO,KAAK,KAAK;AACjB,YAAM,OAAO,QAAQ;AACrB,cAAQ,eAAe,OAAO,aAAa,MAAM,KAAK,SAAS,CAAC;AAAA,IAClE;AACA,UAAM,eAAe,QAAQ;AAC7B,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,KAAC,YAAY;AACX,YAAM,EAAE,KAAK,OAAO,IAAI,MAAM,YAAY,IAAI;AAC9C,YAAM,OAAO,QAAQ;AACrB,UAAI,MAAM,eAAe,IAAI;AAAA,IAC/B,GAAG;AAAA,EACL;AACA,SAAO;AACT;", "names": ["k", "p", "k", "_a", "k2", "k", "p", "p", "p2", "ref", "k", "createHead", "useHead", "createHead"]}