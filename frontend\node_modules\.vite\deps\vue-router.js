import {
  NavigationFailureType,
  RouterLink,
  RouterView,
  START_LOCATION_NORMALIZED,
  createMemoryHistory,
  createRouter,
  createRouterMatcher,
  createWebHashHistory,
  createWebHistory,
  isNavigationFailure,
  loadRouteLocation,
  matchedRouteKey,
  onBeforeRouteLeave,
  onBeforeRouteUpdate,
  parseQuery,
  routeLocation<PERSON>ey,
  router<PERSON>ey,
  routerViewLocationKey,
  stringifyQuery,
  useLink,
  useRoute,
  useRouter,
  viewDepthKey
} from "./chunk-KJ23OONK.js";
import "./chunk-CYLFN3VO.js";
import "./chunk-2TUXWMP5.js";
export {
  NavigationFailureType,
  RouterLink,
  RouterView,
  START_LOCATION_NORMALIZED as START_LOCATION,
  createMemoryHistory,
  createRouter,
  createRouterMatcher,
  createWebHashHistory,
  createWebHistory,
  isNavigationFailure,
  loadRouteLocation,
  matchedRouteKey,
  onBeforeRouteLeave,
  onBeforeRouteUpdate,
  parseQuery,
  routeLocationKey,
  routerKey,
  routerViewLocationKey,
  stringifyQuery,
  useLink,
  useRoute,
  useRouter,
  viewDepthKey
};
